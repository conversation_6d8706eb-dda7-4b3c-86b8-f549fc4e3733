import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { router } from 'expo-router';

// Configure how notifications are handled when the app is in the foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export interface PushNotificationData {
  type: 'trip_started' | 'pickup_confirmed' | 'trip_ended' | 'booking_accepted' | 'booking_declined' | 'payment_request' | 'trip_cancelled';
  tripId: string;
  title: string;
  body: string;
  data?: any;
}

class PushNotificationService {
  private expoPushToken: string | null = null;
  private isInitialized: boolean = false;

  async initialize(): Promise<string | null> {
    try {
      // Try to register for push notifications, but fallback to local-only if it fails
      let token = null;
      try {
        token = await this.registerForPushNotificationsAsync();
        this.expoPushToken = token;
        console.log('✅ Push notifications with FCM token obtained:', token);
      } catch (tokenError) {
        console.warn('⚠️ FCM token failed, using local notifications only:', tokenError);
        // Still allow local notifications to work
        await this.setupLocalNotificationsOnly();
      }

      this.isInitialized = true;

      // Set up notification response listener
      this.setupNotificationResponseListener();

      console.log('✅ Push Notification Service initialized (local notifications ready)');
      return token;
    } catch (error) {
      console.error('❌ Failed to initialize push notification service:', error);
      return null;
    }
  }

  private async setupLocalNotificationsOnly(): Promise<void> {
    // Set up notification channels for Android (works without FCM)
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#473BF0',
        sound: 'default',
      });

      await Notifications.setNotificationChannelAsync('trip-updates', {
        name: 'Trip Updates',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#473BF0',
        sound: 'default',
        description: 'Notifications for trip status updates',
      });
    }

    // Request basic notification permissions
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    if (existingStatus !== 'granted') {
      await Notifications.requestPermissionsAsync();
    }
  }

  private setupNotificationResponseListener() {
    // Handle notification taps when app is in foreground or background
    Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      console.log('📱 Notification tapped:', data);

      // Navigate to home page for all notification types
      router.replace('/(tabs)/Home');
    });
  }

  private async registerForPushNotificationsAsync(): Promise<string | null> {
    let token: string | null = null;

    // Set up notification channels first (works without FCM)
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#473BF0',
        sound: 'default',
      });

      await Notifications.setNotificationChannelAsync('trip-updates', {
        name: 'Trip Updates',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#473BF0',
        sound: 'default',
        description: 'Notifications for trip status updates',
      });

      await Notifications.setNotificationChannelAsync('booking-updates', {
        name: 'Booking Updates',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#34A853',
        sound: 'default',
        description: 'Notifications for booking confirmations and updates',
      });
    }

    if (!Device.isDevice) {
      console.warn('❌ Must use physical device for push notifications');
      throw new Error('Physical device required for push notifications');
    }

    // Request permissions
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      throw new Error('Push notification permission not granted');
    }

    // Try to get Expo push token (requires FCM on Android)
    const projectId = Constants?.expoConfig?.extra?.eas?.projectId;
    if (!projectId) {
      throw new Error('Project ID not found - required for Expo push tokens');
    }

    // This is where the Firebase error occurs
    token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
    console.log('✅ Expo push token obtained:', token);

    return token;
  }

  async sendLocalNotification(notificationData: PushNotificationData): Promise<void> {
    if (!this.isInitialized) {
      console.warn('⚠️ Push notification service not initialized');
      return;
    }

    try {
      const channelId = this.getChannelIdForType(notificationData.type);
      
      await Notifications.scheduleNotificationAsync({
        content: {
          title: notificationData.title,
          body: notificationData.body,
          data: {
            ...notificationData.data,
            type: notificationData.type,
            tripId: notificationData.tripId,
          },
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: null, // Show immediately
        identifier: `${notificationData.type}_${notificationData.tripId}_${Date.now()}`,
      });

      console.log('✅ Local notification sent:', notificationData.title);
    } catch (error) {
      console.error('❌ Failed to send local notification:', error);
    }
  }

  private getChannelIdForType(type: string): string {
    switch (type) {
      case 'trip_started':
      case 'trip_ended':
      case 'pickup_confirmed':
        return 'trip-updates';
      case 'booking_accepted':
      case 'booking_declined':
        return 'booking-updates';
      default:
        return 'default';
    }
  }

  getExpoPushToken(): string | null {
    return this.expoPushToken;
  }

  isServiceInitialized(): boolean {
    return this.isInitialized;
  }

  // Helper methods for common notification types
  async notifyTripStarted(tripId: string, driverName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_started',
      tripId,
      title: '🚗 Trip Started',
      body: `${driverName} has started your ride. Track your progress in real-time.`,
      data: { driverName }
    });
  }

  async notifyPickupConfirmed(tripId: string, passengerName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'pickup_confirmed',
      tripId,
      title: '✅ Pickup Confirmed',
      body: `${passengerName}'s pickup has been confirmed.`,
      data: { passengerName }
    });
  }

  async notifyTripEnded(tripId: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'trip_ended',
      tripId,
      title: '🎉 Trip Completed',
      body: 'Your ride has been completed successfully.',
      data: {}
    });
  }

  async notifyBookingAccepted(tripId: string, driverName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'booking_accepted',
      tripId,
      title: '✅ Booking Accepted',
      body: `Great news! ${driverName} has accepted your ride request.`,
      data: { driverName }
    });
  }

  async notifyBookingDeclined(tripId: string, driverName: string): Promise<void> {
    await this.sendLocalNotification({
      type: 'booking_declined',
      tripId,
      title: '❌ Booking Declined',
      body: `${driverName} couldn't accept your ride request. Don't worry, you can find another trip.`,
      data: { driverName }
    });
  }
}

// Export singleton instance
export const pushNotificationService = new PushNotificationService();

// Helper functions for easy access
export const pushNotificationHelpers = {
  initialize: () => pushNotificationService.initialize(),
  notifyTripStarted: (tripId: string, driverName: string) => 
    pushNotificationService.notifyTripStarted(tripId, driverName),
  notifyPickupConfirmed: (tripId: string, passengerName: string) => 
    pushNotificationService.notifyPickupConfirmed(tripId, passengerName),
  notifyTripEnded: (tripId: string) => 
    pushNotificationService.notifyTripEnded(tripId),
  notifyBookingAccepted: (tripId: string, driverName: string) => 
    pushNotificationService.notifyBookingAccepted(tripId, driverName),
  notifyBookingDeclined: (tripId: string, driverName: string) => 
    pushNotificationService.notifyBookingDeclined(tripId, driverName),
  getToken: () => pushNotificationService.getExpoPushToken(),
  isInitialized: () => pushNotificationService.isServiceInitialized(),
};
