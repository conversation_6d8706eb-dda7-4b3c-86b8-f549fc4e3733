import axios from "axios";
import * as SecureStore from "expo-secure-store";
import { Platform, Alert } from "react-native";

export const axiosInstance = axios.create({
  baseURL: process.env.EXPO_PUBLIC_BASE_URL,
  // timeout: 15000,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
    "User-Agent": `RideHailingApp/${Platform.OS}`,
  },
});

// Request Interceptor
axiosInstance.interceptors.request.use(
  async (config) => {
    try {
      const tokenData = await SecureStore.getItemAsync("authToken");
      console.log("Token data fetched from SecureStore:", tokenData);

      if (!tokenData) {
        console.warn("No token found in SecureStore");
        return config;
      }

      const parsedToken = JSON.parse(tokenData);
      const currentTime = Date.now();
      
      console.log("Token expiration:", new Date(parsedToken.expiration).toISOString());
      console.log("Current time:", new Date(currentTime).toISOString());

      // Check if token exists and is not expired
      if (parsedToken.token && parsedToken.expiration && currentTime < parsedToken.expiration) {
        console.log("Token is valid and not expired");
        config.headers.Authorization = `Bearer ${parsedToken.token}`;
      } else {
        // Token is expired or invalid, clean up and redirect to sign in
        console.warn("Token has expired or is invalid");
        await SecureStore.deleteItemAsync("authToken");
        Alert.alert(
          "Session Expired",
          "Your session has expired. Please sign in again.",
          [
            {
              text: "OK",
              onPress: () => {
                // Use router to navigate to SignIn
                const router = require("expo-router").router;
                router.replace("/(auth)/SignIn");
              },
            },
          ]
        );
        return Promise.reject(new Error("Token expired, please sign in again"));
      }

      return config;
    } catch (error) {
      console.error("Error in request interceptor:", error);
      return Promise.reject(error);
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response Interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const { response } = error;

    if (!response) {
      // No response received, meaning the server might be asleep
      Alert.alert("Error", "Server is unavailable. Please try again later.");
      return Promise.reject(error);
    }

    if (response.status === 503 || response.status === 504) {
      // Retry logic
      const originalRequest = error.config;
      originalRequest._retry = true; // Prevent infinite loop

      // Optionally wait before retrying
      await new Promise((resolve) => setTimeout(resolve, 4000)); // Wait 2 seconds

      return axiosInstance(originalRequest); // Retry the original request
    }

    // Handle other errors
    // Alert.alert(
    //   "Error",
    //   response.data.message || "An error occurred. Please try again."
    // );
    return Promise.reject(error);
  }
);

class Services {
  async sendOtp({ dataBody }: { dataBody: { phoneNumber: string } }) {
    const response = await axiosInstance.post("/auth/send-otp", dataBody);
    return response.data.data;
  }

  async verifyOTp({ dataBody }: { dataBody: { pin: string; pin_id: string } }) {
    const response = await axiosInstance.post("/auth/verify-otp", dataBody);
    return response.data.data;
  }

  async signUp({
    dataBody,
  }: {
    dataBody: {
      firstName: string;
      lastName: string;
      email: string;
      phoneNumber: string;
      password: string;
      hasPersonalVehicle: boolean;
    };
  }) {
    const response = await axiosInstance.post("/auth/signup", dataBody);
    return response.data.data;
  }

  async signIn({
    dataBody,
  }: {
    dataBody: { email: string; password: string };
  }) {
    const response = await axiosInstance.post("/auth/signin", dataBody);
    // Calculate expiration time (current time + 72 hours)
    const expirationTime = new Date().getTime() + 72 * 60 * 60 * 1000;
    const tokenData = JSON.stringify({
      token: response.data.data.token,
      expiration: expirationTime,
    });
    // Store token and expiration time in SecureStore
    await SecureStore.setItemAsync("authToken", tokenData);
    return response.data;
  }

  async requestPasswordReset(email: string) {
    if (!email) {
      throw new Error("Email is required");
    }
    const response = await axiosInstance.get(
      `/auth/request-password-reset/${email}`
    );
    return response.data;
  }

  async verifyPasswordResetOtp({
    dataBody,
  }: {
    dataBody: {
      email: string;
      pin: string;
    };
  }) {
    const response = await axiosInstance.post(
      `/auth/verify-password-reset-otp`,
      dataBody
    );
    return response.data.data;
  }

  async resetPassword({
    dataBody,
  }: {
    dataBody: {
      newPassword: string;
    };
  }) {
    const response = await axiosInstance.post("/auth/reset-password", dataBody);
    return response.data.data;
  }

  async getCurrentUser() {
    try {
      const response = await axiosInstance.get("/user");
      return response.data.data;
    } catch (error) {
      // console.error("Error fetching user data", error);
      throw error;
    }
  }

  async carDetails({
    dataBody,
  }: {
    dataBody: {
      make: string;
      model: string;
      colour: string;
      yearOfManufacture: number;
      chassisNumber: string;
      passengerCapacity: number;
      plateNumber: string;
    };
  }) {
    const response = await axiosInstance.post(
      "/user/verification/car-details",
      dataBody
    );
    return response.data;
  }

  async getCompleteVerification() {
    const response = await axiosInstance.get("/user/verification/complete");
    return response.data;
  }

  async verificationImage({
    dataBody,
  }: {
    dataBody: {
      type: string;
      image: File;
    };
  }) {
    const response = await axiosInstance.post(
      "/user/verification/image",
      dataBody
    );
    return response.data;
  }

  async userIdentity({
    dataBody,
  }: {
    dataBody: {
      type: string;
      value: string;
    };
  }) {
    const response = await axiosInstance.post(
      "/user/verification/identity",
      dataBody
    );
    return response.data;
  }

  async getAvailableCoRides({
    dataBody,
  }: {
    dataBody: {
      pickup: {
        name: string;
        lat: number;
        lng: number;
      };
      dropoff: {
        name: string;
        lat: number;
        lng: number;
      };
      mode: "private" | "carpool";
    };
  }) {
    const response = await axiosInstance.post("/carpool", dataBody);
    return response.data;
  }

  async scheduleTrip({
    dataBody,
  }: {
    dataBody: {
      type: string;
      origin: {
        name: string;
        lat: number;
        lng: number;
      };
      destination: {
        name: string;
        lat: number;
        lng: number;
      };
      stops: {
        name: string;
        lat: number;
        lng: number;
      }[];
      noOfPassengers: number;
      pricePerSeat: number;
      preferences: {
        desc: string;
        value: boolean | null;
      }[];
      timestamp: string;
      mode: "private" | "carpool";
    };
  }) {
    const response = await axiosInstance.post("/carpool/schedule", dataBody);
    return response.data;
  }

  async requestRide({
    dataBody,
  }: {
    dataBody: {
      tripId: string;
      modeOfPayment: string;
      currentLocation: {
        name: string;
        lat: number;
        lng: number;
      };
      pickup: {
        name: string;
        lat: number;
        lng: number;
      };
      dropoff: {
        name: string;
        lat: number;
        lng: number;
      };
      cardId?: string;
    };
  }) {
    const response = await axiosInstance.post("/carpool/requests", dataBody);
    return response.data;
  }

  async getCarpoolRequests(mode: "carpool" | "private", tripId?: string) {
    const response = await axiosInstance.get(
      `/carpool/requests?mode=${mode}&tripId=${tripId}`
    );
    return response.data;
  }

  async acceptRideRequest({ dataBody }: { dataBody: { requestId: string } }) {
    const response = await axiosInstance.post(
      `/carpool/requests/accept`,
      dataBody
    );
    return response.data;
  }

  async rejectRideRequest({ dataBody }: { dataBody: { requestId: string } }) {
    const response = await axiosInstance.post(
      `/carpool/requests/decline`,
      dataBody
    );
    return response.data;
  }

  async cancelTrip(tripId: string | null) {
    const response = await axiosInstance.delete(`/carpool/${tripId}/cancel`);
    return response.data;
  }

  async rateTrip({
    dataBody,
    tripId,
  }: {
    dataBody: { rating: number; review?: string };
    tripId: string;
  }) {
    console.log('🔍 DEBUG: rateTrip called with:', { tripId, dataBody });
    console.log('🔍 DEBUG: API endpoint:', `/carpool/${tripId}/rate`);

    try {
      const response = await axiosInstance.post(
        `/carpool/${tripId}/rate`,
        dataBody
      );
      console.log('✅ DEBUG: rateTrip success:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ DEBUG: rateTrip error:', error);
      console.error('❌ DEBUG: Error response:', error?.response);
      console.error('❌ DEBUG: Error data:', error?.response?.data);
      throw error;
    }
  }

  // Alias for backward compatibility and clearer naming in components
  async submitTripRating({
    dataBody,
  }: {
    dataBody: { tripId: string; rating: number; review?: string };
  }) {
    return this.rateTrip({
      dataBody: { rating: dataBody.rating, review: dataBody.review },
      tripId: dataBody.tripId,
    });
  }

  async payForTrip({ dataBody }: { dataBody: { tripId: string } }) {
    const response = await axiosInstance.post(`/carpool/pay`, dataBody);
    return response.data;
  }

  async changePassword({
    dataBody,
  }: {
    dataBody: {
      oldPassword: string;
      newPassword: string;
    };
  }) {
    const response = await axiosInstance.post(
      "/auth/change-password",
      dataBody
    );
    return response.data;
  }

  async getAblyToken() {
    const response = await axiosInstance.get("/ably/token");
    return response.data;
  }

  async getActiveTrip() {
    const response = await axiosInstance.get("/user/active-trip");
    return response.data;
  }



  async confirmPickUp({ dataBody }: { dataBody: { requestId: string } }) {
    const response = await axiosInstance.post(
      "/carpool/confirm-pickup",
      dataBody
    );
    return response.data;
  }

  async confirmPayment({
    dataBody,
  }: {
    dataBody: { tripId: string; requestId: string };
  }) {
    const response = await axiosInstance.post(
      "/carpool/confirm-payment",
      dataBody
    );

    return response.data;
  }


  async endTrip({ dataBody }: { dataBody: { tripId: string } }) {
    const response = await axiosInstance.post("/carpool/end-trip", dataBody);

    return response.data;
  }

  async cancelRideRequest({ dataBody }: { dataBody: { requestId: string } }) {
    const response = await axiosInstance.post("/carpool/cancel-ride", dataBody);
    return response.data;
  }

  async getTrips(role: string, page?: number, pageSize?: number) {
    const params = new URLSearchParams({ role });
    if (page) params.append('page', page.toString());
    if (pageSize) params.append('pageSize', pageSize.toString());

    const response = await axiosInstance.get(`/user/rides?${params.toString()}`);
    return response.data;
  }

  async getAllTrips(role: string) {
    let allTrips: any[] = [];
    let currentPage = 1;
    let hasMore = true;

    while (hasMore) {
      try {
        const response = await this.getTrips(role, currentPage, 50);

        if (response.data && response.data.length > 0) {
          allTrips = [...allTrips, ...response.data];

          // Check if there are more pages
          if (response.pagination && response.pagination.hasNext) {
            currentPage++;
          } else {
            hasMore = false;
          }
        } else {
          hasMore = false;
        }
      } catch (error) {
        console.error(`Error fetching trips page ${currentPage}:`, error);
        hasMore = false;
      }
    }

    return {
      data: allTrips,
      pagination: {
        totalRecords: allTrips.length,
        totalPages: Math.ceil(allTrips.length / 50),
        page: 1,
        pageSize: allTrips.length,
        hasNext: false,
        hasPrevious: false
      }
    };
  }

  async activatePrivateRide() {
    const response = await axiosInstance.get("/user/activate-private-ride");
    return response.data;
  }

  async deactivatePrivateRide() {
    const response = await axiosInstance.delete("/user/activate-private-ride");
    return response.data;
  }

  async requestPrivateRide({
    dataBody,
  }: {
    dataBody: {
      driverId: string;
      estimatedPrice: number;
      modeOfPayment: string;
      currentLocation: {
        name: string;
        lat: number;
        lng: number;
      };
      pickup: {
        name: string;
        lat: number;
        lng: number;
      };
      dropoff: {
        name: string;
        lat: number;
        lng: number;
      };
      cardId: string;
    };
  }) {
    const response = await axiosInstance.post(
      "/carpool/private/requests",
      dataBody
    );
    return response.data;
  }

  async updateUserAddress({
    dataBody,
  }: {
    dataBody: { name: string; lng: number; lat: number };
  }) {
    const response = await axiosInstance.patch("/user/location", dataBody);
    return response.data;
  }

  async updateAddresses({
    dataBody,
  }: {
    dataBody: { addresses: { lat: number; lng: number; name: string }[] };
  }) {
    const response = await axiosInstance.patch("/user/address", dataBody);
    return response.data;
  }

  async detachCard(cardId: string) {
    const response = await axiosInstance.delete(
      `/stripe/detach-card/${cardId}`
    );
    return response.data;
  }

  async applyPromoCode({ dataBody }: { dataBody: { code: string } }) {
    const response = await axiosInstance.post("/user/promo-code", dataBody);
    return response.data;
  }

  async getTransactions() {
    const response = await axiosInstance.get("/transactions");
    return response.data;
  }

  async getUserDetails(userId: string) {
    const response = await axiosInstance.get(`/user/${userId}`);
    return response.data;
  }
}

export const services = new Services();
