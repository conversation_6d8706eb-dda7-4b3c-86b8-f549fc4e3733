import React, { useRef } from 'react';
import { View, Text, TouchableOpacity, Image, TextInput } from 'react-native';
import { router } from 'expo-router';
import { usePassengerTripEndedStore } from '@/app/store/passengerTripEndedStore';
import CustomModal from '@/components/Modal';
import Button from '@/components/Button';
import { useMutation } from '@tanstack/react-query';
import { services } from '@/services';
import Toast from 'react-native-toast-message';

const PassengerTripEndedModal: React.FC = () => {
  const modalRef = useRef<any>(null);
  
  const {
    showModal,
    tripData,
    isRatingTrip,
    selectedRating,
    reviewText,
    hideTripEndedModal,
    setIsRatingTrip,
    setSelectedRating,
    setReviewText,
    resetState,
  } = usePassengerTripEndedStore();

  // Rating submission mutation
  const { mutate: submitRating, isPending: isSubmittingRating } = useMutation({
    mutationFn: services.submitTripRating, // You'll need to implement this service
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: "Thank you for your feedback!",
        text2: "Your rating has been submitted.",
      });
      handleCloseModal();
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: "Failed to submit rating",
        text2: error?.response?.data?.message || "Please try again later",
      });
    },
  });

  const handleCloseModal = () => {
    modalRef.current?.close();
    setTimeout(() => {
      hideTripEndedModal();
      resetState();
      // Navigate to home screen
      router.replace("/(tabs)/Home");
    }, 300);
  };

  const handleRateTrip = () => {
    setIsRatingTrip(true);
    setTimeout(() => {
      modalRef.current?.expand();
    }, 100);
  };

  const handleSubmitRating = () => {
    if (selectedRating === 0) {
      Toast.show({
        type: "error",
        text1: "Please select a rating",
      });
      return;
    }

    if (tripData) {
      submitRating({
        dataBody: {
          tripId: tripData.tripId,
          rating: selectedRating,
          review: reviewText.trim() || undefined,
        }
      });
    }
  };

  const renderStar = (starNumber: number) => {
    const isSelected = starNumber <= selectedRating;
    return (
      <TouchableOpacity
        key={starNumber}
        onPress={() => setSelectedRating(starNumber)}
        className="mx-1"
      >
        <Text className={`text-2xl ${isSelected ? 'text-yellow-400' : 'text-gray-300'}`}>
          ★
        </Text>
      </TouchableOpacity>
    );
  };

  const getEndReasonText = () => {
    switch (tripData?.endReason) {
      case 'completed':
        return 'Your trip has been completed successfully!';
      case 'early':
        return 'Your trip ended earlier than expected.';
      case 'cancelled':
        return 'Your trip was cancelled.';
      case 'issue':
        return 'Your trip ended due to an issue.';
      default:
        return 'Your trip has ended.';
    }
  };

  const getEndReasonEmoji = () => {
    switch (tripData?.endReason) {
      case 'completed':
        return '🎉';
      case 'early':
        return '⏰';
      case 'cancelled':
        return '🚫';
      case 'issue':
        return '⚠️';
      default:
        return '🎉';
    }
  };

  if (!showModal || !tripData) {
    return null;
  }

  return (
    <CustomModal
      ref={modalRef}
      index={0}
      customSnapPoints={isRatingTrip ? ["60%"] : ["45%"]}
      onClose={handleCloseModal}
    >
      <View className="flex-1 w-full relative bg-[#FFFFFF]">
        <View className="flex-1 items-center justify-center w-[90%] mx-auto">
          
          {!isRatingTrip ? (
            // Trip Ended View
            <>
              <Text className='text-[40px] mb-[6px]'>{getEndReasonEmoji()}</Text>
              
              <Text className="text-xl font-semibold mb-4 text-center">
                Trip Ended
              </Text>
              
              <Text className="text-center text-[15px] font-normal mb-6">
                {getEndReasonText()}
              </Text>

              {/* Trip Summary */}
              <View className="w-full bg-gray-50 rounded-lg p-4 mb-6">
                <View className="flex-row items-center mb-2">
                  <View className="flex-1">
                    <Text className="text-sm font-medium text-gray-600">From</Text>
                    <Text className="text-base font-semibold">{tripData.origin}</Text>
                  </View>
                  <Image 
                    source={require("../assets/images/right_line.png")} 
                    className="w-4 h-4 mx-2" 
                    tintColor="#666"
                  />
                  <View className="flex-1">
                    <Text className="text-sm font-medium text-gray-600">To</Text>
                    <Text className="text-base font-semibold">{tripData.destination}</Text>
                  </View>
                </View>
                
                <View className="flex-row justify-between mt-3 pt-3 border-t border-gray-200">
                  <View>
                    <Text className="text-sm font-medium text-gray-600">Driver</Text>
                    <Text className="text-base font-semibold">{tripData.driverName}</Text>
                  </View>
                  {tripData.cost && (
                    <View>
                      <Text className="text-sm font-medium text-gray-600">Cost</Text>
                      <Text className="text-base font-semibold">{tripData.cost}</Text>
                    </View>
                  )}
                </View>
              </View>

              {/* Action Buttons */}
              <View className="flex-row w-full">
                <Button
                  text="Rate Trip"
                  buttonClassName="bg-[#473BF0] flex-1 mr-2"
                  textClassName="text-white"
                  onClick={handleRateTrip}
                />
                <Button
                  text="Close"
                  buttonClassName="bg-[#F4F4F4] flex-1 ml-2"
                  textClassName="text-[#151B2D]"
                  onClick={handleCloseModal}
                />
              </View>
            </>
          ) : (
            // Rating View
            <>
              <Text className='text-[30px] mb-[6px]'>⭐</Text>
              
              <Text className="text-xl font-semibold mb-4 text-center">
                Rate Your Trip
              </Text>
              
              <Text className="text-center text-[15px] font-normal mb-6">
                How was your experience with {tripData.driverName}?
              </Text>

              {/* Star Rating */}
              <View className="flex-row justify-center mb-6">
                {[1, 2, 3, 4, 5].map(renderStar)}
              </View>

              {/* Review Text Input */}
              <View className="w-full mb-6">
                <Text className="text-sm font-medium text-gray-600 mb-2">
                  Leave a review (optional)
                </Text>
                <TextInput
                  value={reviewText}
                  onChangeText={setReviewText}
                  placeholder="Share your experience..."
                  multiline
                  numberOfLines={3}
                  className="border border-gray-300 rounded-lg p-3 text-base"
                  style={{ textAlignVertical: 'top' }}
                />
              </View>

              {/* Action Buttons */}
              <View className="flex-row w-full">
                <Button
                  text="Back"
                  buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
                  textClassName="text-[#151B2D]"
                  onClick={() => setIsRatingTrip(false)}
                />
                <Button
                  text="Submit"
                  buttonClassName="bg-[#473BF0] flex-1 ml-2"
                  textClassName="text-white"
                  onClick={handleSubmitRating}
                  isLoading={isSubmittingRating}
                  buttonDisabled={isSubmittingRating || selectedRating === 0}
                />
              </View>
            </>
          )}
        </View>
      </View>
    </CustomModal>
  );
};

export default PassengerTripEndedModal;
