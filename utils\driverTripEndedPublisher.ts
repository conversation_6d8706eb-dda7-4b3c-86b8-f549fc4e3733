import { Realtime } from 'ably';
import { TripEndedEventData } from './passengerTripEndedListener';

interface Trip {
  id: string;
  origin: { name: string };
  destination: { name: string };
  passengers: Array<{ id: string; firstName?: string; lastName?: string }>;
  price?: string;
  timestamp?: string;
}

interface Driver {
  id: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
}

class DriverTripEndedPublisher {
  private ably: Realtime | null = null;
  private isInitialized: boolean = false;

  /**
   * Initialize the publisher with Ably instance
   */
  initialize(ably: Realtime): void {
    this.ably = ably;
    this.isInitialized = true;
    console.log('📡 DriverTripEndedPublisher initialized');
  }

  /**
   * Publish trip-ended event to all passengers
   */
  async publishTripEnded(
    trip: Trip, 
    driver: Driver, 
    endReason: 'completed' | 'early' | 'cancelled' | 'issue' = 'completed'
  ): Promise<void> {
    if (!this.isInitialized || !this.ably) {
      console.warn('⚠️ DriverTripEndedPublisher not initialized');
      return;
    }

    try {
      const channelName = `trip-${trip.id}`;
      const channel = this.ably.channels.get(channelName);

      // Calculate trip duration if possible
      const duration = this.calculateTripDuration(trip.timestamp);

      // Prepare event data
      const eventData: TripEndedEventData = {
        tripId: trip.id,
        driverName: `${driver.firstName} ${driver.lastName}`,
        driverPhoto: driver.profilePicture,
        origin: this.formatLocation(trip.origin.name),
        destination: this.formatLocation(trip.destination.name),
        duration,
        cost: trip.price,
        endReason,
        endTime: new Date().toISOString(),
      };

      console.log('🔍 DEBUG: About to publish trip-ended events', {
        channelName,
        eventData,
        passengerCount: trip.passengers.length,
        timestamp: new Date().toISOString()
      });

      // Publish to all passengers
      for (const passenger of trip.passengers) {
        const passengerEventData = {
          ...eventData,
          passengerId: passenger.id, // Include passenger ID for filtering
        };

        console.log('🔍 DEBUG: Publishing to specific passenger', {
          passengerId: passenger.id,
          passengerEventData
        });

        await channel.publish('trip-ended', passengerEventData);
        console.log(`📡 Published trip-ended event to passenger ${passenger.id} on channel:`, channelName);
      }

      // Also publish a general event without passenger ID (for backward compatibility)
      console.log('🔍 DEBUG: Publishing general event', { eventData });
      await channel.publish('trip-ended', eventData);
      console.log('📡 Published general trip-ended event on channel:', channelName);

      console.log('✅ Successfully published trip-ended events for trip:', trip.id);
    } catch (error) {
      console.error('❌ Failed to publish trip-ended event:', error);
      throw error;
    }
  }

  /**
   * Publish trip cancelled event (specific type of trip ended)
   */
  async publishTripCancelled(trip: Trip, driver: Driver, reason?: string): Promise<void> {
    console.log('🚫 Publishing trip cancelled event:', { tripId: trip.id, reason });
    await this.publishTripEnded(trip, driver, 'cancelled');
  }

  /**
   * Publish trip ended early event
   */
  async publishTripEndedEarly(trip: Trip, driver: Driver, reason?: string): Promise<void> {
    console.log('⏰ Publishing trip ended early event:', { tripId: trip.id, reason });
    await this.publishTripEnded(trip, driver, 'early');
  }

  /**
   * Publish trip ended due to issue
   */
  async publishTripEndedWithIssue(trip: Trip, driver: Driver, issue?: string): Promise<void> {
    console.log('⚠️ Publishing trip ended with issue event:', { tripId: trip.id, issue });
    await this.publishTripEnded(trip, driver, 'issue');
  }

  /**
   * Format location name for display
   */
  private formatLocation(locationName: string): string {
    // Take only the first part before comma for cleaner display
    return locationName.split(',')[0].trim();
  }

  /**
   * Calculate trip duration if start time is available
   */
  private calculateTripDuration(startTime?: string): string | undefined {
    if (!startTime) return undefined;

    try {
      const start = new Date(startTime);
      const end = new Date();
      const durationMs = end.getTime() - start.getTime();
      
      const minutes = Math.floor(durationMs / (1000 * 60));
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;

      if (hours > 0) {
        return `${hours}h ${remainingMinutes}m`;
      } else {
        return `${minutes}m`;
      }
    } catch (error) {
      console.warn('⚠️ Failed to calculate trip duration:', error);
      return undefined;
    }
  }

  /**
   * Check if publisher is ready
   */
  isReady(): boolean {
    return this.isInitialized && this.ably !== null;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.ably = null;
    this.isInitialized = false;
    console.log('🧹 DriverTripEndedPublisher cleaned up');
  }
}

// Export singleton instance
export const driverTripEndedPublisher = new DriverTripEndedPublisher();

// Helper functions for easy access
export const driverTripEndedPublisherHelpers = {
  initialize: (ably: Realtime) => 
    driverTripEndedPublisher.initialize(ably),
  publishTripEnded: (trip: Trip, driver: Driver, endReason?: 'completed' | 'early' | 'cancelled' | 'issue') => 
    driverTripEndedPublisher.publishTripEnded(trip, driver, endReason),
  publishTripCancelled: (trip: Trip, driver: Driver, reason?: string) => 
    driverTripEndedPublisher.publishTripCancelled(trip, driver, reason),
  publishTripEndedEarly: (trip: Trip, driver: Driver, reason?: string) => 
    driverTripEndedPublisher.publishTripEndedEarly(trip, driver, reason),
  publishTripEndedWithIssue: (trip: Trip, driver: Driver, issue?: string) => 
    driverTripEndedPublisher.publishTripEndedWithIssue(trip, driver, issue),
  isReady: () => 
    driverTripEndedPublisher.isReady(),
  cleanup: () => 
    driverTripEndedPublisher.cleanup(),
};

export type { Trip, Driver };
