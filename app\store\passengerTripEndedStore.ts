import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface TripEndedData {
  tripId: string;
  driverName: string;
  driverPhoto?: string;
  origin: string;
  destination: string;
  duration?: string;
  distance?: string;
  cost?: string;
  endReason?: 'completed' | 'early' | 'cancelled' | 'issue';
  endTime: string;
}

interface PassengerTripEndedState {
  // Modal visibility
  showModal: boolean;
  
  // Trip data
  tripData: TripEndedData | null;
  
  // Modal state
  isRatingTrip: boolean;
  selectedRating: number;
  reviewText: string;
  
  // Actions
  setShowModal: (show: boolean) => void;
  setTripData: (data: TripEndedData) => void;
  showTripEndedModal: (data: TripEndedData) => void;
  hideTripEndedModal: () => void;
  
  // Rating actions
  setIsRatingTrip: (rating: boolean) => void;
  setSelectedRating: (rating: number) => void;
  setReviewText: (text: string) => void;
  
  // Reset state
  resetState: () => void;
}

const initialState = {
  showModal: false,
  tripData: null,
  isRatingTrip: false,
  selectedRating: 0,
  reviewText: '',
};

export const usePassengerTripEndedStore = create<PassengerTripEndedState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      setShowModal: (show) => set({ showModal: show }),
      
      setTripData: (data) => set({ tripData: data }),
      
      showTripEndedModal: (data) => {
        console.log('🔍 DEBUG: showTripEndedModal called', {
          data,
          timestamp: new Date().toISOString(),
          currentState: get()
        });

        set({
          showModal: true,
          tripData: data,
          isRatingTrip: false,
          selectedRating: 0,
          reviewText: ''
        });

        console.log('🔍 DEBUG: Modal state updated', {
          newState: get(),
          timestamp: new Date().toISOString()
        });
      },
      
      hideTripEndedModal: () => {
        console.log('❌ Hiding passenger trip ended modal');
        set({ 
          showModal: false,
          isRatingTrip: false 
        });
      },
      
      setIsRatingTrip: (rating) => set({ isRatingTrip: rating }),
      
      setSelectedRating: (rating) => set({ selectedRating: rating }),
      
      setReviewText: (text) => set({ reviewText: text }),
      
      resetState: () => {
        console.log('🔄 Resetting passenger trip ended state');
        set(initialState);
      },
    }),
    {
      name: 'passenger-trip-ended-storage',
      // Only persist essential data, not the modal state
      partialize: (state) => ({
        tripData: state.tripData,
      }),
    }
  )
);

// Helper functions for easy access
export const passengerTripEndedHelpers = {
  showModal: (data: TripEndedData) => 
    usePassengerTripEndedStore.getState().showTripEndedModal(data),
  hideModal: () => 
    usePassengerTripEndedStore.getState().hideTripEndedModal(),
  isModalVisible: () => 
    usePassengerTripEndedStore.getState().showModal,
  getTripData: () => 
    usePassengerTripEndedStore.getState().tripData,
  reset: () => 
    usePassengerTripEndedStore.getState().resetState(),
};

export type { TripEndedData };
