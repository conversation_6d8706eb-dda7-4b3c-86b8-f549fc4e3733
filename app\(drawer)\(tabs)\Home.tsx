import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  Dimensions,
  Switch,
  ScrollView,
  RefreshControl,
} from "react-native";
import React, { useEffect, useMemo, useRef, useState } from "react";
import MapView, { PROVIDER_DEFAULT, Circle } from "react-native-maps";
import { useUser } from "../../../context/UserContext";
import CustomModal from "@/components/Modal";
import BottomSheet from "@gorhom/bottom-sheet";
import { Href, router, useNavigation } from "expo-router";
import { DrawerActions } from "@react-navigation/native";
import { useRide } from "@/context/RideProvider";
import Modal from "react-native-modal";
import useCurrentUser from "@/hooks/useCurrentUser";
import { realtimeNotificationService } from "@/utils/realtimeNotifications";
import HomeTripCard from "@/components/HomeTripCard";
import useActiveTrip from "@/hooks/useActiveTrip";
import useGetAblyToken from "@/hooks/useGetAblyToken";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import { RealtimeChannel } from "ably";
import Ably from "ably";
import ActiveRides from "@/components/ActiveRides";
import RoleBasedNotifications, { RoleBasedNotificationsRef } from "@/components/RoleBasedNotifications";
import RoleBasedNotificationTester from "@/components/RoleBasedNotificationTester";
import { queryClient } from "@/providers";
// import img from "@/assets/images/homme.png";

const statusBarHeight = Platform.OS === "android" ? StatusBar.currentHeight : 0;

const Home = () => {
  const { isEnabled, toggleSwitch } = useRide();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const { initialRegion, displayCurrentAddress, updateRide, setRide } =
    useUser();
  const [isInitialNavigation, setIsInitialNavigation] = useState(true);
  const [previousTripId, setPreviousTripId] = useState<string | null>(null);
  const ablyRef = useRef<Ably.Realtime | null>(null);
  const channelRef = useRef<RealtimeChannel | null>(null);
  const roleBasedNotificationsRef = useRef<RoleBasedNotificationsRef>(null);

  const { ablyToken } = useGetAblyToken();

  const screenHeight = Dimensions.get("window").height;

  const navigation = useNavigation();
  const [privateRideModal, setPrivateRideModal] = useState(false);

  const { data: user, isLoading: userDetailsLoading } = useCurrentUser();
  const { data: tripData, refetchActive: refetchActiveTrip } = useActiveTrip();



  const [refreshing, setRefreshing] = useState(false);

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const dynamicSnapPoint = useMemo(() => {
    const minHeight = 270;
    const percentHeight = screenHeight * 0.25;
    return Math.max(minHeight, percentHeight);
  }, [screenHeight]);

  const customSnapPoints = useMemo(
    () => [dynamicSnapPoint],
    [dynamicSnapPoint]
  );

  const { mutate: updateAddress } = useMutation({
    mutationFn: services.updateUserAddress,
    // onSuccess: (data) => {
    //   Toast.show({
    //     type: "success",
    //     text1: data?.message,
    //   });
    // },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  useEffect(() => {
    const updateLocationIfReady = async () => {
      // Check if the required data is available
      if (initialRegion && displayCurrentAddress) {
        updateAddress({
          dataBody: {
            name: displayCurrentAddress,
            lat: initialRegion.latitude,
            lng: initialRegion.longitude,
          },
        });
      } else {
        console.log("Waiting for user location data to load...");
      }
    };

    updateLocationIfReady();
  }, [initialRegion, displayCurrentAddress]);

  // Handle private ride modal
  useEffect(() => {
    if (isEnabled && !privateRideModal && !tripData?.data) {
      const timer = setTimeout(() => {
        navigation.dispatch(DrawerActions.closeDrawer());
        setPrivateRideModal(true);
      }, 300);
  
      return () => clearTimeout(timer);
    }
  }, [isEnabled]);

  // Handle navigation based on active trip
  useEffect(() => {
    // Reset initial navigation flag when trip data changes
    if (tripData?.data?.id !== previousTripId) {
      setIsInitialNavigation(true);
      setPreviousTripId(tripData?.data?.id || null);
    }

    // Don't proceed if no data or no active trip
    if (!tripData?.data || tripData?.message === "no active trip found.") {
      return;
    }

    // Only handle navigation if this is the initial navigation for this trip
    if (!isInitialNavigation) {
      return;
    }

    const timer = setTimeout(() => {
      try {
        const tripDataString = JSON.stringify(tripData.data);
        const isDriver = tripData.data?.driver?.id === user?.id;
        const isPrivateMode = tripData.data?.mode === "private";

        // Navigate based on status, driver, and mode
        if (
          (tripData.data?.status === "pending" ||
            tripData.data?.status === "ongoing") &&
          isDriver
        ) {
          if (isPrivateMode) {
            // Driver in private mode
            router.replace({
              pathname: "/PrivateDriver",
              params: {
                trip: encodeURIComponent(tripDataString),
              },
            } as Href);
          } else {
            // Driver in carpool mode
            router.replace({
              pathname: "/PostRide",
              params: {
                trip: encodeURIComponent(tripDataString),
              },
            } as Href);
          }
        } else {
          if (isPrivateMode) {
            // Passenger in private mode
            router.replace({
              pathname: "/PrivatePassengerMap",
              params: {
                trip: encodeURIComponent(tripDataString),
              },
            } as Href);
          } else {
            // Passenger in carpool mode
            router.replace({
              pathname: "/BookRide",
              params: {
                trip: encodeURIComponent(tripDataString),
              },
            } as Href);
          }
        }

        // Mark this navigation as handled
        setIsInitialNavigation(false);
      } catch (error) {
        console.error("Navigation error:", error);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [tripData, user?.id, isInitialNavigation, previousTripId]);


const initializeAbly = async () => {
  if (!ablyToken?.clientId) {
    console.warn("No Ably client ID available");
    return;
  }

  if (!user || !user.id) {
    console.warn("User data not yet available");
    return;
  }

  try {
    // Clean up existing connection if any
    if (ablyRef.current) {
      await cleanup();
    }

    // Create new Ably instance
    ablyRef.current = new Ably.Realtime({
      key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
      clientId: ablyToken.clientId,
      autoConnect: true,
    });

    // Set up channel
    channelRef.current = ablyRef.current.channels.get("drivers");

    // Subscribe to ride updates
    channelRef.current.subscribe("ride-update", (message) => {
      console.log("Received ride update:", message);
      // Refetch active trip when a ride is updated
      refetchActiveTrip();
      // Also refresh role-based notifications
      roleBasedNotificationsRef.current?.refresh();
    });

    // Initialize realtime notifications if user has an active trip
    if (tripData?.data?.id) {
      const isDriver = tripData.data.driver?.id === user.id;
      const userRole = isDriver ? 'driver' : 'passenger';

      realtimeNotificationService.initialize(
        ablyRef.current,
        tripData.data.id,
        user.id,
        userRole
      );
    }

    // Update ride with clientId
    setRide((prev) => ({
      ...prev,
      clientId: ablyToken.clientId,
    }));

    // Set up presence handlers
    channelRef.current.presence.subscribe("enter", (member) => {
      console.log("Member entered:", member);
    });

    channelRef.current.presence.subscribe("leave", (member) => {
      console.log("Member left:", member);
    });

    // Enter the presence channel with user data
    await channelRef.current.presence.enter({
      clientId: ablyToken.clientId,
      timestamp: new Date().toISOString(),
      userId: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
    });

    // Set up connection state handling
    ablyRef.current.connection.on((stateChange) => {
      console.log("Connection state changed:", stateChange.current);

      if (stateChange.current === "connected") {
        console.log("Connected to Ably");
        channelRef.current?.presence.enter({
          clientId: ablyToken.clientId,
          timestamp: new Date().toISOString(),
        });
      } else if (stateChange.current === "suspended") {
        console.warn("Connection suspended; attempting reconnection.");
      } else if (stateChange.current === "disconnected") {
        console.warn("Connection lost; Ably will attempt to reconnect.");
      } else if (stateChange.current === "failed") {
        console.error("Failed to connect to Ably");
      }
    });
  } catch (error) {
    console.error("Error initializing Ably:", error);
  }
};


  const cleanup = async () => {
    try {
      if (channelRef.current) {
        // Leave presence channel
        await channelRef.current.presence.leave();
        // Unsubscribe from all presence events
        channelRef.current.presence.unsubscribe();
        // Detach from channel
        await channelRef.current.detach();
      }

      if (ablyRef.current) {
        // Close Ably connection
        ablyRef.current.close();
      }

      // Cleanup realtime notifications
      realtimeNotificationService.cleanup();

      // Clear refs
      channelRef.current = null;
      ablyRef.current = null;
    } catch (error) {
      console.error("Error during cleanup:", error);
    }
  };

  // Initialize Ably when token is available
  useEffect(() => {
    if (ablyToken?.clientId) {
      initializeAbly();
    }
    return () => {
      cleanup();
    };
  }, [ablyToken?.clientId]);

  // Update ride clientId when token changes
  useEffect(() => {
    if (ablyToken?.clientId) {
      updateRide("clientId", ablyToken.clientId);
    }
  }, [ablyToken?.clientId]);

  // Add refresh function
  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    try {
      // Refetch all necessary data
      await Promise.all([
        refetchActiveTrip?.(),
        roleBasedNotificationsRef.current?.refresh(),
        queryClient.invalidateQueries({ queryKey: ["trip"] }),
        queryClient.invalidateQueries({ queryKey: ["requests"] })
      ]);
    } catch (error) {
      console.error("Error refreshing:", error);
    } finally {
      setRefreshing(false);
    }
  }, [refetchActiveTrip]);

  // Auto-refresh function for RoleBasedNotifications
  const handleAutoRefresh = React.useCallback(async () => {
    console.log('🔄 Home: Auto-refresh triggered by RoleBasedNotifications');
    try {
      // Refetch all necessary data without showing the refresh indicator
      await Promise.all([
        refetchActiveTrip?.(),
        queryClient.invalidateQueries({ queryKey: ["trip"] }),
        queryClient.invalidateQueries({ queryKey: ["requests"] })
      ]);
      console.log('✅ Home: Auto-refresh completed');
    } catch (error) {
      console.error('❌ Home: Auto-refresh failed:', error);
    }
  }, [refetchActiveTrip]);

  if (userDetailsLoading) {
    return (
      <View className="w-full h-full bg-white items-center justify-center">
        <ActivityIndicator size="small" color="#000" />
      </View>
    );
  }

  return (
    <ScrollView 
      className="h-full w-full bg-[#F4F4F4] relative"
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <StatusBar barStyle="dark-content" backgroundColor="transparent" />

      <Modal
        isVisible={privateRideModal}
        onBackdropPress={() => {
          // toggleSwitch();
          setPrivateRideModal(false);
          console.log("backdrop clicked");
        }}
        backdropOpacity={0.5}
      >
        <View
          style={{
            backgroundColor: "white",
            borderRadius: 16,
            padding: 16,
            height: Platform.OS === "android" ? 250 : 250,
            width: Dimensions.get("window").width * 0.9,
            marginTop:
              Platform.OS === "android"
                ? Dimensions.get("window").height * 0.9
                : 0,
            alignItems: "center",
            justifyContent: "center",
            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: 2,
            },
          }}
        >
          {Platform.OS === "android" && (
            <TouchableOpacity
              onPressOut={() => {
                // toggleSwitch();
                setPrivateRideModal(false);
              }}
              className="items-end w-full float-right"
            >
              {/* <XIcon color="#000" /> */}
            </TouchableOpacity>
          )}
          <Image
            source={require("../../../assets/images/Private.png")}
            style={{ height: 38, width: 38 }}
            resizeMode="contain"
          />
          <Text style={{ color: "#151B2D", fontSize: 20, fontWeight: "500" }}>
            Private trip mode
          </Text>
          <Text
            style={{
              textAlign: "center",
              color: "#151B2D",
              fontSize: 13,
              lineHeight: 20,
              marginVertical: 10,
            }}
          >
            Want to make extra cash? With private trips you get on demand ride
            requests from users who need to go somewhere but don't want to
            carpool.
          </Text>
          <TouchableOpacity
            activeOpacity={0.8}
            onPressOut={() => {
              setPrivateRideModal(false);
              router.push("/(ride)/PrivateDriver");
            }}
            style={{
              backgroundColor: "#473BF0",
              width: "100%",
              height: 40,
              justifyContent: "center",
              alignItems: "center",
              borderRadius: 100,
            }}
          >
            <Text style={{ color: "white", fontSize: 14, fontWeight: "600" }}>
              Continue
            </Text>
          </TouchableOpacity>
        </View>
      </Modal>

      <View className="w-[90%] mx-auto mt-10">
        <View className="flex-row justify-between items-center">
          <View className="flex-row items-center">
            <Image
              source={{ uri: user?.profilePicture || "https://images.unsplash.com/photo-1488161628813-04466f872be2?q=80&w=1964&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"}}
              className="w-10 h-10 rounded-full mr-2"
              resizeMode="cover"
            />
            <Text className="text-[#151B2D] font-semibold text-lg">
              Hi, {user ? user.firstName : "John"}!
            </Text>
          </View>
          {/* <View className="flex-row items-center">
            <Text className="text-[#151B2D] mr-2">Private Mode</Text>
            <Switch
              value={isEnabled}
              onValueChange={toggleSwitch}
              thumbColor={isEnabled ? "#473BF0" : "#f4f3f4"}
              trackColor={{ false: "#767577", true: "#81b0ff" }}
            />
          </View> */}
        </View>
        <View className="flex-row justify-between mt-7">
          
        <TouchableOpacity
          activeOpacity={0.9}
          onPress={() => {
            console.log(user);
            try {
              router.push("/(ride)/FindRideScreen" as Href);
              Toast.show({
                type: 'success',
                text1: 'Navigation successful',
                text2: 'Taking you to ride options',
                position: 'bottom',
              });
            } catch (error: any) {
              console.error("Navigation error:", error);
              Toast.show({
                type: 'error',
                text1: 'Navigation failed',
                text2: error.message || 'An error occurred while navigating',
                position: 'bottom',
              });
            }
          }}
          className="gap-y-2 bg-white w-[48%] justify-center p-4 border shadow-sm border-white rounded-[6px]"
        >
            <Image
              source={require("../../../assets/images/FindRideNew.png")}
              className="w-[30px] h-[30px]"
              resizeMode="contain"
            />
            <Text className="text-[#151B2D] font-medium text-[15px]">
              Find ride
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() =>
              user?.isVerified
                ? router.push("/(ride)/OfferRide" as Href)
                : router.push("/(verifyDriver)/Verification" as Href)
            }
            className="gap-y-2 bg-white w-[48%] justify-center p-4 border shadow-sm border-white rounded-[6px]"
          >
            <Image
              source={require("../../../assets/images/OfferRideNew.png")}
              className="w-[30px] h-[30px]"
              resizeMode="contain"
            />
            <Text className="text-[#151B2D] font-medium text-[15px]">
              Post trip
            </Text>
          </TouchableOpacity>
        </View>

        

        {/* Show active trip card if user has an active trip */}
        {tripData && (
          <HomeTripCard
            trip={tripData}
            currentUser={user}
            arrivalTime={tripData.arrivalTime ? new Date(tripData.arrivalTime) : undefined}
          />
        )}

        <View className="font-semibold text-[#151B2D] mt-7">
          <Text className="text-2xl font-bold">Inbox</Text>
          <Text className="text-sm text-gray-500">
            {new Date().toLocaleDateString('en-US', { weekday: 'short', day: '2-digit', year: 'numeric' })}
          </Text>
        </View>

        
      </View>

      <View className="flex-1 items-center rounded-lg m-5 bg-white">
        {/* Role-based real-time notifications */}
        <RoleBasedNotifications
          ref={roleBasedNotificationsRef}
          onAutoRefresh={handleAutoRefresh}
        />

        {/* Notification system tester - remove in production */}
        {/* {__DEV__ && <RoleBasedNotificationTester />} */}

        <ActiveRides />

          {/* <Text className="text-lg font-medium mb-2">Plan your Coride </Text>
          <Text className="text-gray-500 mb-4">Your trip activity will appear here</Text>
          <Image
            source={require('../../../assets/images/homme.png')}
            className="w-24 h-24"
          /> */}
        </View>


        {/* end */}

      {/* <TouchableOpacity
          activeOpacity={0.9}
          onPress={() => router.push("/(ride)/PrivatePassenger" as Href)}
          className="gap-y-2 bg-white w-full flex-row justify-center mt-2 items-center p-4 border shadow-sm border-white rounded-[6px]"
        >
          <Image
            source={require("../../../assets/images/Private.png")}
            className="w-[30px] h-[30px]"
            resizeMode="contain"
          />
          <Text className="text-[#151B2D] font-medium text-[15px] text-center ml-2">
            Find a private driver
          </Text>
        </TouchableOpacity> */}
    </ScrollView>

  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    paddingTop: statusBarHeight,
    flex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});

export default Home;
