import { View, Text, KeyboardAvoidingView, Platform, ScrollView, TouchableOpacity } from 'react-native'
import React, { useState, useEffect, useMemo, useRef } from 'react'
import { router, useLocalSearchParams } from "expo-router";
import { services } from '@/services';
import { useMutation, useQuery } from '@tanstack/react-query';
import Toast from 'react-native-toast-message';
import { useRide } from '@/context/RideProvider';
import { queryClient } from '@/providers';
import Button from '@/components/Button';
import { useUser } from '@/context/UserContext';
import MapView, { PROVIDER_DEFAULT } from 'react-native-maps';
import useActiveTrip from '@/hooks/useActiveTrip';
import useCurrentUser from '@/hooks/useCurrentUser';
import { Realtime } from "ably";
import { Image } from 'react-native';
import { makePhoneCall } from '@/utils/helpers';
import GoBack from '@/components/GoBack';
import * as Location from 'expo-location';
import BottomSheet from "@gorhom/bottom-sheet";
import CustomModal from '@/components/Modal';
import {BottomSheetTextInput} from '@gorhom/bottom-sheet';
import { useTripStore } from '../store/tripStore';
import { notificationHelpers } from '@/utils/notificationHelpers';
import { realtimeNotificationService, realtimeNotificationHelpers } from '@/utils/realtimeNotifications';
import TripStateDebugger from '@/components/TripStateDebugger';
import { useNavigation } from '@react-navigation/native';
import TemporaryDriverDetails from '@/components/TemporaryDriverDetails';


interface Location {
    lat: number;
    lng: number;
    name: string;
}

interface Preference {
    desc: string;
    value: boolean;
}

interface Passenger {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    isVerified: boolean;
    currentLocation: Location;
    pickup: Location;
    dropoff: Location;
    modeOfPayment: string;
    requestId: string;
    requestedAt: string;
    status?: string;
}

interface Trip {
    id?: string;
    type: string;
    origin: Location;
    destination: Location;
    stops: Location[];
    noOfPassengers: number;
    pricePerSeat: number;
    preferences: Preference[];
    timestamp: string;
    mode: "private" | "carpool";
    isPreview?: boolean;
    driverNote?: string;
    driver?: UserDetails;
    passengers?: Array<Passenger>;
    status?: string;
}

interface UserDetails {
    id: string;
    firstName: string;
    lastName: string;
    verification?: {
        carDetails?: {
            make: string;
            model: string;
            colour: string;
            chassisNumber: string;
        };
    };
}

interface ApiError extends Error {
    response?: {
        data?: {
            description?: string;
            message?: string;
        };
    };
}


const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371;
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
};

const deg2rad = (deg: number): number => deg * (Math.PI / 180);

const formatDate = (timestamp: string): string => {
    if (!timestamp) return 'Date not available';

    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return 'Invalid date';

    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

    const dayOfWeek = days[date.getDay()];
    const day = date.getDate();
    const month = months[date.getMonth()];

    let hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours ? hours : 12;

    return `${dayOfWeek}, ${day} ${month} at ${hours}:${minutes} ${ampm}`;
};

const capitalizeFirstLetter = (text: string): string => {
    return text.split(" ").map(word => 
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(" ");
};

interface TripRouteCardProps {
    trip: Trip;
    arrivalTime: Date | null;
    tripStarted: boolean;
    tripProgress: number;
}

interface TripDetailsCardProps {
    trip: Trip;
}


const TripRouteCard: React.FC<TripRouteCardProps> = ({ trip, arrivalTime, tripStarted, tripProgress }) => (
    <View className="px-4 mt-2 w-full justify-center items-center">
        <View className="bg-white flex-row items-center rounded-[6px] px-2 py-4">
            <View className="flex-1 gap-y-[2px] ml-3">
                <View className="flex flex-row gap-2 items-center">
                    <View className="h-[30px] w-[2px] bg-[#473BF0]" />
                    <View className='flex-1 flex-col'>
                        <View className='flex flex-row items-center justify-between'>
                            <Text className='font-semibold text-sm'>
                                {trip?.origin?.name.split(' ')[0]}
                            </Text>
                            <Text className={`${tripStarted && tripProgress < 100 ? "text-[#E5850C]" : ""} font-medium text-sm`}>
                                {tripStarted && tripProgress < 100 ? "Ongoing" : formatDate(trip.timestamp)}
                            </Text>
                        </View>
                        <Text numberOfLines={1} ellipsizeMode="tail" className="text-sm font-normal text-[#787A80]">
                            {trip?.origin?.name}
                        </Text>
                    </View>
                </View>
                <View className="flex flex-row gap-2 items-center mb-2">
                    <View className="h-[30px] w-[2px] bg-[#34A853]" />
                    <View className='flex-1 flex-col'>
                        <View className='flex flex-row items-center justify-between'>
                            <Text className='font-semibold text-sm'>
                                {trip?.destination?.name.split(' ')[0]}
                            </Text>
                            <Text className={`${tripProgress >= 100 ? "text-[#34A853]" : ""} font-medium text-sm`}>
                                {tripProgress >= 100
                                    ? "Arrived"
                                    : arrivalTime
                                        ? `~${formatDate(arrivalTime.toISOString()).split('at ')[1]}`
                                        : ""
                                }
                            </Text>
                        </View>
                        <Text numberOfLines={1} ellipsizeMode="tail" className="text-sm font-normal text-[#787A80]">
                            {trip?.destination?.name}
                        </Text>
                    </View>
                </View>
                {tripStarted && (
                            <View className="mt-2">
                              <View className="relative">
                                {/* Progress Bar Background */}
                                <View className="h-[4px] bg-gray-200 rounded-full">
                                  <View
                                    className="h-[4px] bg-[#34A853] rounded-full"
                                    style={{ width: `${tripProgress}%` }}
                                  />
                                </View>
                
                                <View
                                  className="absolute -top-[6px] transform -translate-x-1/2"
                                  style={{
                                    left: `${Math.min(Math.max(tripProgress, 5), 95)}%`,
                                  }}
                                >
                                  <Image
                                    source={require("../../assets/images/movingCar.png")}
                                    className="w-[32px] h-[16px]"
                                    resizeMode="contain"
                                  />
                                </View>
                              </View>
                            </View>
                          )}
            </View>
        </View>
    </View>
);

const TripDetailsCard: React.FC<TripDetailsCardProps> = ({ trip }) => {
    const noOfPassengers = trip?.noOfPassengers || 0;
    const currentPassengers = trip?.passengers?.length || 0;
    const seatsLeft = noOfPassengers - currentPassengers;
    const pricePerSeat = trip?.pricePerSeat || 0;

    return (
        <View className='flex flex-row px-4 mt-2 w-full justify-between items-center space-x-2'>
            <View className='flex-1 flex-row justify-between bg-white p-4 rounded-[6px]'>
                <Text className='text-[#787A80] font-semibold text-sm'>Seats left:</Text>
                <Text className='font-semibold text-sm'>{seatsLeft}/{noOfPassengers}</Text>
            </View>
            <View className='flex-1 flex-row justify-between items-center bg-white p-4 rounded-[6px]'>
                <Text className='text-[#787A80] font-semibold text-sm'>Price per seat:</Text>
                <Text className='text-[#473BF0] font-semibold text-sm'>₦{pricePerSeat}</Text>
            </View>
        </View>
    );
};

const TripSummary: React.FC = () => {
    const { trip: tripParams } = useLocalSearchParams();
    const { data: activeTrip, hasActiveTrip } = useActiveTrip();
    const { data: currentUser } = useCurrentUser();
    const { setIsEnabled } = useRide();
    const { initialRegion } = useUser();
    const navigation = useNavigation();

    const trip = useMemo(() => {
        try {
            if (!tripParams || typeof tripParams !== 'string') return null;
            return JSON.parse(decodeURIComponent(tripParams)) as Trip;
        } catch (error) {
            console.error('Error parsing trip params:', error);
            return null;
        }
    }, [tripParams]);

    const { data: rideRequests, refetch } = useQuery({
        queryKey: ["requests", trip?.id],
        queryFn: () => services.getCarpoolRequests("carpool", trip?.id),
    });



    const {
        // Trip Status
        currentTripId,
        setCurrentTripId,
        tripStarted,
        setTripStarted,
        tripProgress,
        setTripProgress,
        showSeats,
        setShowSeats,
        driverNote,
        
        // Modals
        showEndTripModal,
        setShowEndTripModal,
        showPaymentModal,
        setShowPaymentModal,
        showPassengerEndTripModal,
        setShowPassengerEndTripModal,
        showEndTripOptions,
        setShowEndTripOptions,
        
        // End Trip Options
        selectedOption,
        setSelectedOption,
        reportIssue,
        setReportIssue,
        selectedIssue,
        setSelectedIssue,
        somethingElse,
        setSomethingElse,
        customIssueText,
        setCustomIssueText,
        showEndTripEarly,
        setShowEndTripEarly,
        
        // Request Statuses
        requestStatuses,
        setRequestStatuses,
        updateRequestStatus,
        resetTripState,
        resetForNewTrip
    } = useTripStore();

    const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
    const endTripModalRef = useRef<BottomSheet>(null);
    const paymentModalRef = useRef<BottomSheet>(null);
    const ablyRef = useRef<Realtime | null>(null);
    const [loadingRequestId, setLoadingRequestId] = useState<{ id: string | null; type: "accept" | "reject" | null }>({ id: null, type: null });

    // Countdown timer state
    const [countdown, setCountdown] = useState<string>('');
    const [isTimeToStart, setIsTimeToStart] = useState<boolean>(false);

    const isDriver = useMemo(() => {
        // Check both activeTrip and trip parameter to determine if user is driver
        const activeTripDriverId = activeTrip?.driver?.id;
        const tripDriverId = trip?.driver?.id;

        return currentUser?.id === activeTripDriverId || currentUser?.id === tripDriverId;
    }, [currentUser?.id, activeTrip?.driver?.id, trip?.driver?.id]);

    const arrivalTime = useMemo(() => {
        if (!trip?.origin || !trip?.destination || !trip?.timestamp) return null;

        // Validate coordinates
        const originLat = trip.origin.lat;
        const originLng = trip.origin.lng;
        const destLat = trip.destination.lat;
        const destLng = trip.destination.lng;

        if (isNaN(originLat) || isNaN(originLng) || isNaN(destLat) || isNaN(destLng)) {
            return null;
        }

        const distance = calculateDistance(originLat, originLng, destLat, destLng);

        const averageSpeed = 60;
        const travelTimeHours = distance / averageSpeed;
        const departureTime = new Date(trip.timestamp);

        if (isNaN(departureTime.getTime())) return null;

        return new Date(departureTime.getTime() + (travelTimeHours * 60 * 60 * 1000));
    }, [trip]);

    useEffect(() => {
        if (!trip?.id || !currentUser?.id) return;

        ablyRef.current = new Realtime({
            key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
            clientId: currentUser?.id,
            autoConnect: true,
        });

        // Initialize realtime notification service
        const userRole = isDriver ? 'driver' : 'passenger';
        realtimeNotificationService.initialize(
            ablyRef.current,
            trip.id,
            currentUser.id,
            userRole
        );

        const channel = ablyRef.current.channels.get("drivers");
        const tripChannel = ablyRef.current.channels.get(trip.id);

        channel.subscribe("ride-request", (message) => {
            refetch(); 
        });

        // Subscribe to trip end event
        tripChannel.subscribe("trip-ended", (message) => {
            if (!isDriver) {
                setShowPassengerEndTripModal(true);
                setTimeout(() => {
                    endTripModalRef.current?.expand();
                }, 100);
            }
        });

        // Subscribe to payment confirmation
        tripChannel.subscribe("confirm-payment", (message) => {
            if (message.data) {
                setShowPaymentModal(true);
                setTimeout(() => {
                    paymentModalRef.current?.expand();
                }, 100);
            }
        });

        // Subscribe to progress updates (for passengers)
        tripChannel.subscribe("progress-update", (message) => {
            if (!isDriver && message.data) {
                console.log('📱 Passenger received progress update:', message.data.progress + '%');
                setTripProgress(message.data.progress);
            }
        });

        return () => {
            channel.unsubscribe();
            tripChannel.unsubscribe();
            realtimeNotificationService.cleanup();
            ablyRef.current?.close();
        };
    }, [trip?.id, currentUser?.id, refetch, isDriver]);

    useEffect(() => {
        if (rideRequests?.data?.length > 0) {
          console.log("There are ride requests available")
        }
    
        if (
          activeTrip?.status === "pending" &&
          activeTrip?.passengers?.length === activeTrip?.noOfPassengers
        ) {
            console.log("Sorry, the ride is full")
        }
    
        if (activeTrip?.status === "ongoing") {
            console.log("The ride is currently is currently in progress")
        }
    }, [rideRequests, activeTrip]);

    const isPassengerOnThisTrip = useMemo(() => {
        return trip?.passengers?.some((p: { id: string }) => p.id === currentUser?.id);
    }, [trip?.passengers, currentUser?.id]);

    const currentPassenger = useMemo(() => {
        return trip?.passengers?.find(p => p.id === currentUser?.id);
    }, [trip?.passengers, currentUser?.id]);


    const handleAcceptRideRequest = (requestId: string) => {
        setLoadingRequestId({ id: requestId, type: "accept" });
        acceptRideRequest({ dataBody: { requestId } });
      };
      
      const handleRejectRideRequest = (requestId: string) => {
        setLoadingRequestId({ id: requestId, type: "reject" });
        rejectRideRequest({ dataBody: { requestId } });
      };

    // Note: Start trip is now handled locally without API call

    const handleStartTrip = () => {
        if (activeTrip?.passengers?.length > 0) {
            console.log('🚀 Starting trip:', {
                tripId: trip?.id,
                currentTripId,
                activeStatus: activeTrip?.status,
                currentTripStarted: tripStarted
            });
            setTripStarted(true);
            Toast.show({
                type: "success",
                text1: "Your trip has started",
            });

            // Send realtime notification to passengers
            if (trip?.id && currentUser?.firstName) {
                // Pass complete trip data for proper navigation
                const completeTrip = {
                    ...trip,
                    driver: trip.driver || {
                        id: currentUser.id,
                        firstName: currentUser.firstName,
                        lastName: currentUser.lastName,
                        email: currentUser.email,
                        phoneNumber: currentUser.phoneNumber,
                        isVerified: currentUser.isVerified,
                        carDetails: currentUser.carDetails
                    },
                    passengers: activeTrip?.passengers || [],
                    status: 'ongoing' // Mark as ongoing since trip is started
                };

                realtimeNotificationHelpers.notifyTripStarted(
                    trip.id,
                    currentUser.firstName,
                    completeTrip // Pass complete trip object instead of subset
                );

                // Also publish direct event to trip channel (like driver notifications)
                if (ablyRef.current) {
                    const tripChannel = ablyRef.current.channels.get(`trip-${trip.id}`);
                    console.log('📤 Publishing trip-started to channel:', `trip-${trip.id}`);
                    tripChannel.publish("trip-started", {
                        tripDetails: {
                            id: trip.id,
                            driverName: `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
                            origin: trip.origin,
                            destination: trip.destination,
                            timestamp: trip.timestamp,
                            pricePerSeat: trip.pricePerSeat,
                            passengers: activeTrip?.passengers
                        },
                        timestamp: new Date().toISOString()
                    });
                }
            }
        } else {
            Toast.show({
                type: "error",
                text1: "Cannot start trip without passengers",
            });
        }
    };

    const handleEndTrip = () => {
        console.log('End Trip Pressed - Current Trip Progress:', tripProgress);
        setShowEndTripModal(true);
        setTimeout(() => {
            endTripModalRef.current?.expand();
        }, 100);
    };

    const handleConfirmEndTrip = () => {
        console.log('Confirm End Trip Pressed - Current Trip Progress:', tripProgress);
        console.log('Selected Option:', selectedOption);
        console.log('Selected Issue:', selectedIssue);
        if (trip?.id) {
            // Allow ending trip if progress is 100% OR if ending early
            if (tripProgress >= 100 || selectedOption === 'option1' || selectedIssue) {
                console.log('Ending trip with conditions:', {
                    tripProgress,
                    selectedOption,
                    selectedIssue
                });
                endTrip({ dataBody: { tripId: trip.id } });
            }
        }
    };

    const handleConfirmPayment = (passengerId: string) => {
        console.log('Starting payment confirmation with passengerId:', passengerId);
        console.log('Current activeTrip:', activeTrip);
        
        if (!trip?.id) {
            console.log('No trip ID found');
            return;
        }
        
        // Find the passenger to get their requestId
        const passenger = activeTrip?.passengers?.find((p: Passenger) => p.id === passengerId);
        console.log('Found passenger:', passenger);
        
        if (!passenger?.requestId) {
            console.log('No requestId found for passenger:', passenger);
            Toast.show({
                type: "error",
                text1: "Trip could not be started",
            });
            return;
        }

        console.log('Calling confirmPayment with:', {
            tripId: trip.id,
            requestId: passenger.requestId
        });

        confirmPayment({ dataBody: { tripId: trip.id, requestId: passenger.requestId } });
        paymentModalRef.current?.close();
        setShowPaymentModal(false);
    };

    useEffect(() => {
        if (!hasActiveTrip && !trip) {
            router.replace("/(tabs)/Home");
        }
    }, [hasActiveTrip, trip]);

    const isPreview = trip?.isPreview === true;

    const { mutate: scheduleTrip, isPending: isPostingTrip } = useMutation({
        mutationFn: services.scheduleTrip,
        onSuccess: (data) => {
            Toast.show({
                type: "success",
                text1: data.message || "Trip posted successfully!",
            });

            queryClient.invalidateQueries({ queryKey: ["trip"] });


            setTimeout(() => {
                router.replace("/(tabs)/Home");
            }, 1500);
        },
        onError: (error: ApiError) => {
            console.log('Full error response:', error.response);
            Toast.show({
                type: "error",
                text1: error.response?.data?.description || error.response?.data?.message || error.message,
            });
        },
    });

    // Driver cancel trip mutation
    const { mutate: handleCancelTrip, isPending: isCancelingTrip } = useMutation({
        mutationFn: services.cancelTrip,
        onSuccess: (data) => {
            Toast.show({
                type: "success",
                text1: data.message,
            });

            setIsEnabled(false);

            queryClient.invalidateQueries({ queryKey: ["trip"] });
            setTimeout(() => {
                router.replace("/(tabs)/Home");
            }, 1000);
        },
        onError: (error: ApiError) => {
            console.log('Full error response:', error.response);
            Toast.show({
                type: "error",
                text1: error.response?.data?.description || error.response?.data?.message || error.message,
            });
        },
    });

    // Passenger cancel ride request mutation
    const { mutate: handleCancelRideRequest, isPending: isCancelingRideRequest } = useMutation({
        mutationFn: services.cancelRideRequest,
        onSuccess: async (data) => {
            Toast.show({
                type: "success",
                text1: data.message || "Ride cancelled successfully",
            });



            queryClient.invalidateQueries({ queryKey: ["trip"] });
            queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
            queryClient.invalidateQueries({ queryKey: ["requests"] });
            setTimeout(() => {
                router.replace("/(tabs)/Home");
            }, 1000);
        },
        onError: (error: ApiError) => {
            Toast.show({
                type: "error",
                text1: error.response?.data?.description || error.response?.data?.message || error.message,
            });
        },
    });

    const handlePostTrip = () => {
        if (!trip) return;
        
        const tripData = {
            type: trip.type,
            origin: trip.origin,
            destination: trip.destination,
            stops: trip.stops,
            noOfPassengers: trip.noOfPassengers,
            pricePerSeat: trip.pricePerSeat,
            preferences: trip.preferences,
            timestamp: trip.timestamp,
            mode: trip.mode as "private" | "carpool",
            driverNote: driverNote
        };

        scheduleTrip({ dataBody: tripData });
    };

    const handleGoBack = () => {
        // If we're showing seats, go back to trip summary instead of Home
        if (showSeats) {
            setShowSeats(false);
        } else {
            router.replace("/(tabs)/Home");
        }
    };

    // Update the start trip condition to check for confirmed pickups
    const canStartTrip = useMemo(() => {
        if (!activeTrip?.status || activeTrip.status !== "pending") return false;

        console.log('Can Start Trip Check:', {
            activeTripStatus: activeTrip?.status,
            activeTripData: activeTrip,
            requestStatuses
        });
        
        const hasConfirmedPickup = Object.values(requestStatuses).some(
            (status) => {
                const typedStatus = status as { status: 'pending' | 'accepted' | 'rejected'; pickupConfirmed: boolean };
                return typedStatus.status === 'accepted' && typedStatus.pickupConfirmed;
            }
        );
        
        return hasConfirmedPickup;
    }, [activeTrip?.status, requestStatuses]);

    const { mutate: confirmPickUp, isPending: isConfirmingPickup } = useMutation({
        mutationFn: services.confirmPickUp,
        onSuccess: (data, variables) => {
            Toast.show({
                type: "success",
                text1: data.message || "Pickup confirmed!",
            });

            // Update the request status with pickup confirmed
            updateRequestStatus(variables.dataBody.requestId, {
                status: 'accepted',
                pickupConfirmed: true
            });

            // Find the passenger name for notifications
            const confirmedPassenger = activeTrip?.data?.passengers?.find(
                (p: Passenger) => p.requestId === variables.dataBody.requestId
            );
            const passengerName = confirmedPassenger ? confirmedPassenger.firstName : 'Passenger';

            // Update active trip data directly
            if (activeTrip?.data) {
                const updatedPassengers = activeTrip.data.passengers?.map((passenger: Passenger) => {
                    if (passenger.requestId === variables.dataBody.requestId) {
                        return { ...passenger, status: 'pickup_confirmed' };
                    }
                    return passenger;
                });

                queryClient.setQueryData(['activeTrip'], {
                    ...activeTrip,
                    data: {
                        ...activeTrip.data,
                        passengers: updatedPassengers
                    }
                });
            }

            // Send realtime notification to passengers
            if (trip?.id) {
                // Pass complete trip data for proper navigation
                const completeTrip = {
                    ...trip,
                    driver: trip.driver || {
                        id: currentUser?.id,
                        firstName: currentUser?.firstName,
                        lastName: currentUser?.lastName,
                        email: currentUser?.email,
                        phoneNumber: currentUser?.phoneNumber,
                        isVerified: currentUser?.isVerified,
                        carDetails: currentUser?.carDetails
                    },
                    passengers: activeTrip?.passengers || [],
                    status: 'ongoing'
                };

                realtimeNotificationHelpers.notifyPickupConfirmed(trip.id, passengerName, completeTrip);
            }

            queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
            refetch();

            if (ablyRef.current && trip?.id) {
                const tripChannel = ablyRef.current.channels.get(`trip-${trip.id}`);
                console.log('📤 Publishing pickup-confirmed to channel:', `trip-${trip.id}`);

                const pickupData = {
                    tripDetails: {
                        id: trip.id,
                        driverName: `${currentUser?.firstName} ${currentUser?.lastName || ''}`.trim(),
                        origin: trip.origin,
                        destination: trip.destination,
                        timestamp: trip.timestamp,
                        pricePerSeat: trip.pricePerSeat,
                    },
                    passengerName: passengerName,
                    requestId: variables.dataBody.requestId,
                    timestamp: new Date().toISOString()
                };

                tripChannel.publish("pickup-confirmed", pickupData);

                // Also publish to passenger's user channel
                if (confirmedPassenger?.id) {
                    const userChannelName = `user-${confirmedPassenger.id}`;
                    const userChannel = ablyRef.current.channels.get(userChannelName);
                    console.log('📤 Publishing pickup-confirmed to user channel:', userChannelName);
                    userChannel.publish("pickup-confirmed", pickupData);
                }
            }
        },
        onError: (error: ApiError) => {
            Toast.show({
                type: "error",
                text1: error.response?.data?.description || error.response?.data?.message || error.message,
            });
        },
    });

    
    useEffect(() => {
        const currentStatuses = { ...requestStatuses };
        let hasChanges = false;

        // Handle accepted passengers (from activeTrip.data.passengers)
        if (activeTrip?.data?.passengers) {
            activeTrip.data.passengers.forEach((passenger: any) => {
                if (passenger.requestId) {
                    const existing = currentStatuses[passenger.requestId];

                    if (!existing) {
                        // Add new accepted passenger
                        currentStatuses[passenger.requestId] = {
                            status: 'accepted' as const,
                            pickupConfirmed: passenger.status === 'pickup_confirmed'
                        };
                        hasChanges = true;
                    } else {
                        // Update existing passenger but preserve pickupConfirmed if already true
                        const shouldUpdatePickup = passenger.status === 'pickup_confirmed' && !existing.pickupConfirmed;
                        if (shouldUpdatePickup) {
                            currentStatuses[passenger.requestId] = {
                                ...existing,
                                pickupConfirmed: true
                            };
                            hasChanges = true;
                        }
                    }
                }
            });
        }

        // Handle pending ride requests (from rideRequests.data)
        if (rideRequests?.data) {
            rideRequests.data.forEach((request: any) => {
                if (request.requestId) {
                    const existing = currentStatuses[request.requestId];

                    // Only initialize if not already in our store (preserve existing states)
                    if (!existing) {
                        // These are pending requests that haven't been accepted/rejected yet
                        currentStatuses[request.requestId] = {
                            status: 'pending' as const,
                            pickupConfirmed: false
                        };
                        hasChanges = true;
                    }
                }
            });
        }

        if (hasChanges) {
            setRequestStatuses(currentStatuses);
        }
    }, [activeTrip?.data?.passengers, rideRequests?.data]); // Include rideRequests in dependencies

    // Handle trip ID changes and reset state if needed
    useEffect(() => {
        if (trip?.id && trip.id !== currentTripId) {
            // New trip detected, reset state for this trip
            resetForNewTrip(trip.id);
        }
    }, [trip?.id, currentTripId, resetForNewTrip]);

    // Ensure preview trips always have tripStarted = false
    useEffect(() => {
        if (trip?.isPreview === true && tripStarted) {
            console.log('Preview trip detected, resetting tripStarted to false');
            setTripStarted(false);
        }
    }, [trip?.isPreview, tripStarted, setTripStarted]);

    // Countdown timer logic
    useEffect(() => {
        // Use activeTrip data if available, otherwise fall back to trip params
        const tripData = activeTrip || trip;
        const timestamp = tripData?.timestamp;

        if (!timestamp || tripStarted) {
            setCountdown('');
            setIsTimeToStart(false);
            return;
        }

        const updateCountdown = () => {
            const now = new Date();
            const tripTime = new Date(timestamp);

            const timeDiff = tripTime.getTime() - now.getTime();

            if (timeDiff <= 0) {
                // Time to start the trip
                setCountdown('');
                setIsTimeToStart(true);
            } else {
                // Calculate countdown
                const hours = Math.floor(timeDiff / (1000 * 60 * 60));
                const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

                let countdownText = '';
                if (hours > 0) {
                    countdownText = `${hours}h ${minutes}m ${seconds}s`;
                } else if (minutes > 0) {
                    countdownText = `${minutes}m ${seconds}s`;
                } else {
                    countdownText = `${seconds}s`;
                }

                setCountdown(countdownText);
                setIsTimeToStart(false);
            }
        };

        // Update immediately
        updateCountdown();

        // Update every second
        const interval = setInterval(updateCountdown, 1000);

        return () => clearInterval(interval);
    }, [activeTrip?.timestamp, trip?.timestamp, tripStarted]);

    // Sync tripStarted state with actual trip status from API
    // Only sync when API confirms the trip status has changed
    useEffect(() => {
        console.log('🔄 Trip sync effect running:', {
            activeStatus: activeTrip?.status,
            tripId: trip?.id,
            currentTripId,
            tripStarted,
            shouldSync: activeTrip?.status && trip?.id === currentTripId
        });

        if (activeTrip?.status && trip?.id === currentTripId) {
            const apiTripStarted = activeTrip.status === 'ongoing' || activeTrip.status === 'in_progress';

            // Only sync if API status is definitive (don't reset based on pending status)
            if (apiTripStarted && !tripStarted) {
                // API says trip is started but local state says it's not - sync to started
                console.log('🔄 Syncing trip state: API says started, updating local state');
                setTripStarted(true);
            } else if (activeTrip.status === 'completed' && tripStarted) {
                // API says trip is completed - reset local state
                console.log('🔄 Syncing trip state: API says completed, resetting local state');
                setTripStarted(false);
            } else {
                console.log('🔄 No sync needed:', {
                    apiTripStarted,
                    localTripStarted: tripStarted,
                    apiStatus: activeTrip.status
                });
            }
            // Don't reset tripStarted to false if API status is still "pending"
            // This allows local state to persist until API catches up
        }
    }, [activeTrip?.status, tripStarted, setTripStarted, trip?.id, currentTripId]);

    // Control header visibility based on showSeats state
    useEffect(() => {
        navigation.setOptions({
            headerShown: !showSeats,
        });
    }, [showSeats, navigation]);

    const { mutate: endTrip, isPending: isEndingTrip } = useMutation({
        mutationFn: services.endTrip,
        onSuccess: (data) => {
            Toast.show({
                type: "success",
                text1: data?.message,
            });
            setShowEndTripModal(false);
            setShowPassengerEndTripModal(true);



            // Send realtime notification to passengers
            if (trip?.id && currentUser?.firstName) {
                realtimeNotificationHelpers.notifyTripEnded(trip.id, currentUser.firstName);
            }

            router.replace("/(tabs)/Home");

            // Only show payment modal if trip is completed and not ended early
            // if (tripProgress >= 100 && !selectedOption && !selectedIssue) {
            //     // Publish payment confirmation event
            //     if (ablyRef.current && trip?.id) {
            //         const tripChannel = ablyRef.current.channels.get(trip.id);
            //         tripChannel.publish("confirm-payment", { timestamp: new Date().toISOString() });
            //     }
            // }

        },
        onError: (error: ApiError) => {
            Toast.show({
                type: "error",
                text1: error.response?.data?.description || error.response?.data?.message || error.message,
            });
        },
    });

    const { mutate: confirmPayment, isPending: isConfirmingPayment } = useMutation({
        mutationFn: services.confirmPayment,
        onSuccess: (data) => {
            console.log('Payment confirmation success:', data);
            Toast.show({
                type: "success",
                text1: data?.message,
            });
            setShowPaymentModal(false);
            router.replace("/(tabs)/Home");
        },
        onError: (error: ApiError) => {
            console.log('Payment confirmation error:', error);
            console.log('Error response:', error.response);
            Toast.show({
                type: "error",
                text1: error.response?.data?.description || error.response?.data?.message || error.message,
            });
        },
    });

    const PassengersPaymentSection = () => {
        const passengers = activeTrip?.passengers || [];

        if (!passengers.length) {
            return (
                <View className='px-4 mt-2'>
                    <TouchableOpacity onPress={() => console.log(activeTrip)} className='bg-white p-4 rounded-[6px]'>
                        <Text className='font-semibold text-base mb-3'>Passengers & Payment</Text>
                        <Text className='text-sm text-[#787A80]'>No passengers yet</Text>
                    </TouchableOpacity>
                </View>
            );
        }

        return (
            <View className='px-4 mt-2'>
                <View className='bg-white p-4 rounded-[6px]'>
                    <Text className='font-semibold text-sm text-[#787A80] mb-3'>Payment method</Text>
                    {passengers.map((passenger: any, index: number) => (
                        <View key={passenger.id || index} className="flex-row justify-between items-center py-2 px-4 rounded-md bg-[#F4F4F4] mb-3">
                            <View className="flex-row items-center">
                                <View className="h-8 w-8 bg-[#EDEDED] rounded-full mr-2 justify-center items-center">
                                    <Text className="text-lg font-bold">
                                        {passenger.firstName?.[0] || '?'}
                                    </Text>
                                </View>
                                <View>
                                    <Text className="font-medium text-sm">
                                        {passenger.firstName}
                                    </Text>
                                </View>
                            </View>
                            <View className="items-end">
                                <Text className="text-sm font-medium text-[#787A80]">
                                    {passenger.modeOfPayment}
                                </Text>
                            </View>
                        </View>
                    ))}
                </View>
            </View>
        );
    };

    // Update the bottom button section
    const renderBottomButton = () => {
        if (!trip) return null;
        
        if (trip.isPreview) {
            return (
                <Button
                    isLoading={isPostingTrip}
                    buttonDisabled={isPostingTrip}
                    text="Post Trip"
                    buttonClassName="bg-[#473BF0] w-full"
                    textClassName="text-white"
                    onClick={handlePostTrip}
                />
            );
        }

        if (isDriver) {
            if (!tripStarted) {
                return (
                    <View className='bg-white'>
                        <Button
                            isLoading={false}
                            buttonDisabled={!isTimeToStart}
                            text={countdown ? `Start in ${countdown}` : isTimeToStart ? "Start Trip" : "Waiting..."}
                            buttonClassName={`${isTimeToStart ? "bg-[#473BF0]" : "bg-gray-400"} w-full`}
                            textClassName="text-white"
                            onClick={handleStartTrip}
                        />
                        <Button
                            isLoading={isCancelingTrip}
                            buttonDisabled={isCancelingTrip}
                            text={isDriver ? "Cancel Trip" : "Cancel Ride"}
                            buttonClassName="bg-[#F4F4F4] w-full mt-2"
                            textClassName="text-black"
                            onClick={() => trip.id && handleCancelTrip(trip.id)}
                        />
                    </View>
                );
            } else {
                return (
                    <View className="space-y-2">
                        <Button
                            isLoading={isEndingTrip}
                            buttonDisabled={isEndingTrip}
                            text="End Trip"
                            buttonClassName="bg-[#473BF0] w-full"
                            textClassName="text-white"
                            onClick={handleEndTrip}
                        />

                        {/* Example: Custom button to show modal to passengers */}
                        {/* <Button
                            text="Send Message to Passengers"
                            buttonClassName="bg-green-500 w-full"
                            textClassName="text-white"
                            onClick={() => {
                                if (trip?.id && currentUser?.firstName) {
                                    realtimeNotificationHelpers.sendCustomMessage(
                                        trip.id,
                                        "Message from Driver",
                                        "The driver has sent you a message: 'Running 5 minutes late due to traffic. Thank you for your patience!'",
                                        "passenger",
                                        currentUser.firstName
                                    );
                                }
                            }}
                        /> */}
                    </View>
                );
            }
        }

        // For passengers, cancel their ride request instead of the entire trip
        const handlePassengerCancel = () => {
            const currentPassenger = trip?.passengers?.find((p: any) => p.id === currentUser?.id);
            if (currentPassenger?.requestId) {
                handleCancelRideRequest({ dataBody: { requestId: currentPassenger.requestId } });
            } else {
                Toast.show({
                    type: "error",
                    text1: "Unable to cancel ride - request not found",
                });
            }
        };

        return (
            <Button
                isLoading={isCancelingRideRequest}
                buttonDisabled={isCancelingRideRequest}
                text={isDriver ? "Cancel Trip" : "Cancel Ride"}
                buttonClassName="bg-[#EDEDED] w-full"
                textClassName="text-[#151B2D]"
                onClick={handlePassengerCancel}
            />
        );
    };

    // Add modals
    const renderModals = () => {
        if (isDriver) {
            return (
                <>
                    {showEndTripModal && (
                        <CustomModal
                            ref={endTripModalRef}
                            index={0}
                            customSnapPoints={["30%"]}
                        >
                            <View className="flex-1 w-full relative bg-[#FFFFFF]">
                                
                            {(!showEndTripOptions && !showEndTripEarly) &&  (
                                <View className="flex-1 items-center justify-center w-[90%] mx-auto">
                                        <>
                                            {tripProgress >= 100 
                                                ? <Text className='text-[40px] mb-[6px]' >🎉</Text>
                                                : <Image source={require("../../assets/images/endTripNew.png")} className='w-[40px] h-[40px]' />
                                            }
                                            
                                            <Text className="text-xl font-semibold mb-4 text-center">
                                                {tripProgress >= 100 ? "Trip Ended" : "End Trip"}
                                            </Text>
                                            <Text className="text-center text-[15px] font-normal mb-6">
                                                {tripProgress >= 100 
                                                    ? "Press 'Okay' to return home"
                                                    : "Are you sure you want to end this trip early?"}
                                            </Text>
                                            {tripProgress >= 100 
                                                ?   <Button
                                                        text="Okay"
                                                        buttonClassName="bg-[#F4F4F4] mb-3"
                                                        textClassName="text-black"
                                                        onClick={handleConfirmEndTrip}
                                                    />
                                                :   <View className="flex-row justify-between">
                                                        
                                                        <Button
                                                            text="Yes"
                                                            buttonClassName="bg-[#473BF0] flex-1 mr-2"
                                                            textClassName="text-white"
                                                            onClick={() => {
                                                                setShowEndTripOptions(true)
                                                                console.log('Confirm End Trip Pressed - Current Trip Progress:', tripProgress);
                                                            }}
                                                        />
                                                        <Button
                                                            text="No"
                                                            buttonClassName="bg-[#F4F4F4] flex-1 ml-2"
                                                            textClassName="text-[#151B2D]"
                                                            onClick={() => {
                                                                endTripModalRef.current?.close();
                                                                setShowEndTripModal(false);
                                                            }}
                                                        />
                                                    </View>
                                            }
                                            
                                        </>
                                    
                                    
                                </View>
                                
                            )}

                                {showEndTripOptions && (
                                        <View className="flex-1 p-2 w-[90%] mx-auto">
                                            <Text className='font-semibold text-[17px] mb-4'>Reason for ending trip</Text>
                                            <View>
                                                <View className="flex-row items-center mb-[18px]">
                                                    <TouchableOpacity 
                                                        onPress={() => setSelectedOption('option1')}
                                                        className="flex-row items-center"
                                                    >
                                                        <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedOption === 'option1' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                                                            {selectedOption === 'option1' && (
                                                                <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                                                            )}
                                                        </View>
                                                        <Text className='text-[15px] font-normal'>Trip completed earlier than expected time</Text>
                                                    </TouchableOpacity>
                                                </View>
                                                <View className="flex-row items-center">
                                                    <TouchableOpacity 
                                                        onPress={() => setSelectedOption('option2')}
                                                        className="flex-row items-center"
                                                    >
                                                        <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedOption === 'option2' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                                                            {selectedOption === 'option2' && (
                                                                <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                                                            )}
                                                        </View>
                                                        <Text className='text-[15px] font-normal'>Report and issue</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                            <View className="flex-row justify-between mb-2 mt-8">

                                                <Button
                                                    text="Cancel"
                                                    buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
                                                    textClassName="text-[#151B2D]"
                                                    onClick={() => {
                                                        setShowEndTripOptions(false)
                                                    }}
                                                />
                                                
                                                <Button
                                                    text="Continue"
                                                    buttonClassName="bg-[#473BF0] flex-1 ml-2"
                                                    textClassName="text-white"
                                                    onClick={() => {
                                                        if (selectedOption === 'option1') {
                                                            setShowEndTripOptions(false)
                                                            setShowEndTripEarly(true)
                                                            setTimeout(() => {
                                                                handleConfirmEndTrip()
                                                            }, 2000);
                                                        } else if (selectedOption === 'option2') {
                                                            setShowEndTripModal(false)
                                                            setReportIssue(true)
                                                        }
                                                    }}
                                                />
                                                
                                            </View>
                                        </View>
                                    )}
                            
                            </View>
                        </CustomModal>
                    )}

                    {reportIssue && (
                        <CustomModal
                            ref={endTripModalRef}
                            index={0}
                            customSnapPoints={["35%"]}
                        >
                            <View className="flex-1 w-full relative bg-[#FFFFFF]">
                            <View className="flex-1 p-2 w-[90%] mx-auto">
                                            <Text className='font-semibold text-[17px] mb-4'>Report an issue</Text>
                                            <View>
                                                <View className="flex-row items-center mb-[18px]">
                                                    <TouchableOpacity 
                                                        onPress={() => setSelectedIssue('firstIssue')}
                                                        className="flex-row items-center"
                                                    >
                                                        <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'firstIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                                                            {selectedIssue === 'firstIssue' && (
                                                                <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                                                            )}
                                                        </View>
                                                        <Text className='text-[15px] font-normal'>Car broke down (flat tire, low fuel etc)</Text>
                                                    </TouchableOpacity>
                                                </View>
                                                <View className="flex-row items-center mb-[18px]">
                                                    <TouchableOpacity 
                                                        onPress={() => setSelectedIssue('secondIssue')}
                                                        className="flex-row items-center"
                                                    >
                                                        <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'secondIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                                                            {selectedIssue === 'secondIssue' && (
                                                                <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                                                            )}
                                                        </View>
                                                        <Text className='text-[15px] font-normal'>Changing routes</Text>
                                                    </TouchableOpacity>
                                                </View>
                                                <View className="flex-row items-center">
                                                    <TouchableOpacity 
                                                        onPress={() => setSelectedIssue('thirdIssue')}
                                                        className="flex-row items-center"
                                                    >
                                                        <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'thirdIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                                                            {selectedIssue === 'thirdIssue' && (
                                                                <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                                                            )}
                                                        </View>
                                                        <Text className='text-[15px] font-normal'>Something else</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                            <View className="flex-row justify-between mb-2 mt-8">

                                                <Button
                                                    text="Back"
                                                    buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
                                                    textClassName="text-[#151B2D]"
                                                    onClick={() => {
                                                        setReportIssue(false)
                                                        setShowEndTripModal(true)
                                                        setShowEndTripOptions(true)
                                                    }}
                                                />
                                                
                                                <Button
                                                    text="Continue"
                                                    buttonClassName="bg-[#473BF0] flex-1 ml-2"
                                                    textClassName="text-white"
                                                    onClick={() => {
                                                        if (selectedIssue === 'firstIssue') {
                                                            setReportIssue(false)
                                                            setShowEndTripEarly(true)
                                                        } else if (selectedIssue === 'secondIssue') {
                                                            setReportIssue(false)
                                                            setShowEndTripEarly(true)
                                                        } else if (selectedIssue === 'thirdIssue') {
                                                            setReportIssue(false)
                                                            setSomethingElse(true)
                                                        }
                                                    }}
                                                />
                                                
                                            </View>
                                        </View>
                            </View>
                        </CustomModal>
                    )}

                    {somethingElse && (
                        <CustomModal
                            ref={endTripModalRef}
                            index={0}
                            customSnapPoints={["48%"]}
                        >
                            <View className="flex-1 w-full relative bg-[#FFFFFF]">
                                <View className="flex-1 p-2 w-[90%] mx-auto">
                                    <Text className='font-semibold text-[17px] mb-4'>Report an issue</Text>
                                    <View>
                                        <View className="flex-row items-center mb-[18px]">
                                            <TouchableOpacity 
                                                onPress={() => setSelectedIssue('firstIssue')}
                                                className="flex-row items-center"
                                            >
                                                <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'firstIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                                                    {selectedIssue === 'firstIssue' && (
                                                        <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                                                    )}
                                                </View>
                                                <Text className='text-[15px] font-normal'>Car broke down (flat tire, low fuel etc)</Text>
                                            </TouchableOpacity>
                                        </View>
                                        <View className="flex-row items-center mb-[18px]">
                                            <TouchableOpacity 
                                                onPress={() => setSelectedIssue('secondIssue')}
                                                className="flex-row items-center"
                                            >
                                                <View className={`h-5 w-5 rounded-full border-2 mr-[10px] ${selectedIssue === 'secondIssue' ? 'border-[#473BF0]' : 'border-gray-300'}`}>
                                                    {selectedIssue === 'secondIssue' && (
                                                        <View className="h-3 w-3 rounded-full bg-[#473BF0] m-auto" />
                                                    )}
                                                </View>
                                                <Text className='text-[15px] font-normal'>Changing routes</Text>
                                            </TouchableOpacity>
                                        </View>
                                        <View className="flex-row items-center">
                                            <Text className='text-[15px] font-medium'>Something else</Text>
                                        </View>
                                        <View className="mt-4">
                                            <BottomSheetTextInput
                                                value={customIssueText}
                                                onChangeText={setCustomIssueText}
                                                placeholder="Please describe the issue..."
                                                multiline
                                                numberOfLines={3}
                                                style={{
                                                    backgroundColor: "#F4F4F4",
                                                    padding: 12,
                                                    borderRadius: 6,
                                                    fontSize: 14,
                                                    color: "#151B2D",
                                                    minHeight: 100,
                                                    textAlignVertical: 'top'
                                                }}
                                            />
                                        </View>
                                    </View>
                                    <View className="flex-row justify-between mb-2 mt-6">
                                        <Button
                                            text="Back"
                                            buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
                                            textClassName="text-[#151B2D]"
                                            onClick={() => {
                                                setSomethingElse(false)
                                                setReportIssue(true)
                                            }}
                                        />
                                        
                                        <Button
                                            text="Continue"
                                            buttonClassName="bg-[#473BF0] flex-1 ml-2"
                                            textClassName="text-white"
                                            onClick={() => {
                                                setSomethingElse(false)
                                                setShowEndTripEarly(true)
                                            }}
                                        />
                                    </View>
                                </View>
                            </View>
                        </CustomModal>
                    )}

                    {showEndTripEarly && (
                        <CustomModal
                            ref={endTripModalRef}
                            index={0}
                            customSnapPoints={["30%"]}
                        >
                                <View className="flex-1 p-2 items-center w-[90%] mx-auto">
                            <Image source={require("../../assets/images/tripEndedNew.png")} className='h-[40px] w-[40px] mb-4' />
                            <Text className='font-semibold text-xl text-center mb-[6px]'>Trip ended</Text>
                            <Text className='text-[15px] font-normal text-center mb-6'>
                                {selectedOption === 'option1' 
                                    ? 'Trip completed earlier than expected time'
                                    : 'Report and issue'
                                }
                                {selectedIssue === 'firstIssue' 
                                    ? 'Car broke down (flat tire, low fuel etc)'
                                    : selectedIssue === 'secondIssue' 
                                        ? 'Changing routes'
                                        : customIssueText
                                }
                            </Text>
                            <Button
                                text="Okay"
                                buttonClassName="bg-[#F4F4F4]"
                                textClassName="text-black"
                                onClick={handleConfirmEndTrip}
                            />
                        </View>
                        </CustomModal>
                            
                    )}

                    

                    {showPaymentModal && (
                        <CustomModal
                            ref={paymentModalRef}
                            index={0}
                            customSnapPoints={["50%"]}
                        >
                            <View className="flex-1 w-full relative bg-[#FFFFFF]">
                                <View className="flex-1 w-[90%] mx-auto">
                                    <Text className="text-xl font-bold mb-4 text-center">
                                        Confirm Payments
                                    </Text>
                                    {activeTrip?.passengers?.map((passenger: Passenger) => (
                                        <View key={passenger.id} className="flex-row justify-between items-center mb-4">
                                            <Text className="font-medium">
                                                {passenger.firstName} {passenger.lastName}
                                            </Text>
                                            <Button
                                                text="Confirm Payment"
                                                buttonClassName="bg-[#34A853]"
                                                textClassName="text-white"
                                                onClick={() => handleConfirmPayment(passenger.id)}
                                                isLoading={isConfirmingPayment}
                                            />
                                        </View>
                                    ))}
                                </View>
                            </View>
                        </CustomModal>
                    )}
                </>
            )
        }

        return (
            <>
                {showPassengerEndTripModal && (
                    <CustomModal
                        ref={endTripModalRef}
                        index={0}
                        customSnapPoints={["30%"]}
                    >
                        <View className="flex-1 items-center justify-center w-[90%] mx-auto">
                            <Text className='text-[40px] mb-[6px]'>🎉</Text>
                            <Text className="text-xl font-semibold mb-4 text-center">
                                Trip Ended
                            </Text>
                            <Text className="text-center text-[15px] font-normal mb-6">
                                We hope you enjoyed your trip!
                            </Text>
                            <Button
                                text="Okay"
                                buttonClassName="bg-[#F4F4F4] mb-3"
                                textClassName="text-black"
                                onClick={() => {
                                    setShowPassengerEndTripModal(false);
                                    router.replace("/(tabs)/Home");
                                }}
                            />
                        </View>
                    </CustomModal>
                )}
            </>
        )
        
    };

    // Update the location tracking effect
    useEffect(() => {
        let locationSubscription: Location.LocationSubscription;

        const startLocationTracking = async () => {
            if (!tripStarted || !trip) return;

            const { status } = await Location.requestForegroundPermissionsAsync();
            if (status !== 'granted') {
                Toast.show({
                    type: "error",
                    text1: "Location permission denied",
                });
                return;
            }

            locationSubscription = await Location.watchPositionAsync(
                {
                    accuracy: Location.Accuracy.High,
                    timeInterval: 5000,
                    distanceInterval: 10,
                },
                (location) => {
                    const currentLocation = {
                        lat: location.coords.latitude,
                        lng: location.coords.longitude,
                        name: ''
                    };
                    setCurrentLocation(currentLocation);

                    // Calculate progress
                    if (trip.origin && trip.destination) {
                        const totalDistance = calculateDistance(
                            trip.origin.lat,
                            trip.origin.lng,
                            trip.destination.lat,
                            trip.destination.lng
                        );

                        const distanceTraveled = calculateDistance(
                            trip.origin.lat,
                            trip.origin.lng,
                            currentLocation.lat,
                            currentLocation.lng
                        );

                        const progress = Math.min((distanceTraveled / totalDistance) * 100, 100);
                        setTripProgress(progress);

                        // Broadcast progress update to passengers via Ably
                        if (ablyRef.current && trip.id && isDriver) {
                            const tripChannel = ablyRef.current.channels.get(`trip-${trip.id}`);
                            tripChannel.publish("progress-update", {
                                progress: progress,
                                currentLocation: currentLocation,
                                timestamp: new Date().toISOString()
                            });
                            console.log('📤 Broadcasting progress update:', progress + '%');
                        }
                    }
                }
            );
        };

        startLocationTracking();

        return () => {
            if (locationSubscription) {
                locationSubscription.remove();
            }
        };
    }, [tripStarted, trip]);

    // Note: Removed cleanup effect that was resetting trip state on unmount
    // This was causing state to be lost when navigating between screens

    const { mutate: acceptRideRequest } = useMutation({
        mutationFn: services.acceptRideRequest,
        onSuccess: (data) => {
            Toast.show({
                type: "success",
                text1: data.message,
            });
            setLoadingRequestId({ id: null, type: null });
            updateRequestStatus(loadingRequestId.id!, {
                status: 'accepted',
                pickupConfirmed: false
            });
            refetch();

            // Send real-time notification to passenger about booking acceptance
            if (trip?.id && currentUser?.firstName) {
                realtimeNotificationHelpers.notifyBookingAccepted(
                    trip.id,
                    `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
                    {
                        id: trip.id,
                        driverName: `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
                        origin: trip.origin,
                        destination: trip.destination,
                        timestamp: trip.timestamp,
                        pricePerSeat: trip.pricePerSeat,
                        // Include full trip data for complete navigation
                        fullTripData: trip
                    }
                );
            }

            if (ablyRef.current) {
                const channel = ablyRef.current.channels.get("drivers");
                channel.publish("ride-update", {
                    type: "request-accepted",
                    tripId: trip?.id,
                    requestId: loadingRequestId.id,
                    timestamp: new Date().toISOString()
                });

                // Also publish to trip channel for passenger notifications
                const tripChannelName = `trip-${trip?.id}`;
                const tripChannel = ablyRef.current.channels.get(tripChannelName);
                console.log('📤 Publishing booking-accepted to channel:', tripChannelName);
                console.log('📤 Driver Ably connection state:', ablyRef.current.connection.state);
                console.log('📤 Trip ID being used:', trip?.id);

                const bookingData = {
                    tripDetails: {
                        id: trip?.id,
                        driverName: `${currentUser?.firstName} ${currentUser?.lastName || ''}`.trim(),
                        origin: trip?.origin,
                        destination: trip?.destination,
                        timestamp: trip?.timestamp,
                        pricePerSeat: trip?.pricePerSeat,
                    },
                    fullTripData: trip, // Include complete trip data for navigation
                    timestamp: new Date().toISOString()
                };

                tripChannel.publish("booking-accepted", bookingData);

                // Also publish to passenger's user channel
                const acceptedRequest = rideRequests?.data?.find((req: any) => req.requestId === loadingRequestId.id);
                if (acceptedRequest?.id) {
                    const userChannelName = `user-${acceptedRequest.id}`;
                    const userChannel = ablyRef.current.channels.get(userChannelName);
                    console.log('📤 Publishing booking-accepted to user channel:', userChannelName);
                    userChannel.publish("booking-accepted", bookingData);
                }
            }

            queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
        },
        onError: (error: any) => {
            setLoadingRequestId({ id: null, type: null });
            Toast.show({
                type: "error",
                text1: error.response
                    ? error.response.data.description || error.response.data.message
                    : error.message,
            });
        },
    });

    const { mutate: rejectRideRequest } = useMutation({
        mutationFn: services.rejectRideRequest,
        onSuccess: (data) => {
            Toast.show({
                type: "success",
                text1: data.message,
            });
            setLoadingRequestId({ id: null, type: null });
            updateRequestStatus(loadingRequestId.id!, {
                status: 'rejected',
                pickupConfirmed: false
            });

            // Send real-time notification to passenger about booking decline
            if (trip?.id && currentUser?.firstName) {
                realtimeNotificationHelpers.notifyBookingDeclined(
                    trip.id,
                    `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
                    {
                        id: trip.id,
                        driverName: `${currentUser.firstName} ${currentUser.lastName || ''}`.trim(),
                        origin: trip.origin,
                        destination: trip.destination,
                        timestamp: trip.timestamp,
                        pricePerSeat: trip.pricePerSeat,
                    }
                );
            }

            // Also publish to trip channel for passenger notifications
            if (ablyRef.current) {
                const tripChannelName = `trip-${trip?.id}`;
                const tripChannel = ablyRef.current.channels.get(tripChannelName);
                console.log('📤 Publishing booking-declined to channel:', tripChannelName);
                console.log('📤 Driver Ably connection state:', ablyRef.current.connection.state);
                console.log('📤 Trip ID being used:', trip?.id);

                const bookingData = {
                    tripDetails: {
                        id: trip?.id,
                        driverName: `${currentUser?.firstName} ${currentUser?.lastName || ''}`.trim(),
                        origin: trip?.origin,
                        destination: trip?.destination,
                        timestamp: trip?.timestamp,
                        pricePerSeat: trip?.pricePerSeat,
                    },
                    fullTripData: trip, // Include complete trip data for navigation
                    timestamp: new Date().toISOString()
                };

                tripChannel.publish("booking-declined", bookingData);

                // Also publish to passenger's user channel
                const rejectedRequest = rideRequests?.data?.find((req: any) => req.requestId === loadingRequestId.id);
                if (rejectedRequest?.id) {
                    const userChannelName = `user-${rejectedRequest.id}`;
                    const userChannel = ablyRef.current.channels.get(userChannelName);
                    console.log('📤 Publishing booking-declined to user channel:', userChannelName);
                    userChannel.publish("booking-declined", bookingData);
                }
            }

            setTimeout(() => {
                refetch();
            }, 500);
        },
        onError: (error: any) => {
            setLoadingRequestId({ id: null, type: null });
            Toast.show({
                type: "error",
                text1: error.response
                    ? error.response.data.description || error.response.data.message
                    : error.message,
            });
        },
    });

    if (!trip) {
        return (
            <View className="flex-1 justify-center items-center bg-[#F4F4F4]">
                <Text className="text-lg font-medium">Error loading trip details</Text>
            </View>
        );
    }

    // Check if trip data is incomplete (missing essential fields)
    const isIncompleteTrip = !trip.origin || !trip.destination || !trip.timestamp;

    if (isIncompleteTrip) {
        return (
            <View className="flex-1 justify-center items-center bg-[#F4F4F4] px-4">
                <Text className="text-lg font-medium text-center mb-2">Incomplete Trip Data</Text>
                <Text className="text-sm text-gray-600 text-center mb-4">
                    Some trip details are missing. Please try refreshing or go back to the home screen.
                </Text>
                <TouchableOpacity
                    onPress={() => router.replace("/(tabs)/Home")}
                    className="bg-[#473BF0] px-6 py-3 rounded-md"
                >
                    <Text className="text-white font-medium">Go to Home</Text>
                </TouchableOpacity>
            </View>
        );
    }

    return (
        <View className='flex-1 bg-[#F4F4F4]'>
            {/* Debug component - remove in production */}
            {/* <TripStateDebugger /> */}

            {showSeats ? (
                <View className='flex-1 bg-[#F4F4F4]'>
                    {/* Back button for seats view */}
                    <View className='px-4 mt-4 mb-2'>
                        <TouchableOpacity
                            onPress={() => setShowSeats(false)}
                            className='flex-row items-center'
                        >
                            <Image
                                source={require("../../assets/images/Back.png")}
                                className="h-[30px] w-[30px] mr-2"
                                resizeMode="contain"
                            />
                            {/* <Text className='text-[#473BF0] font-medium'>Back to Trip Summary</Text> */}
                        </TouchableOpacity>
                    </View>

                    <View className='px-4'>
                        <Text className='text-2xl font-semibold'>Meet your Coriders</Text>
                        <Text className='text-base font-normal'>Here is everyone that will be joining your trip</Text>
                    </View>
            
                    <View className='px-4 flex-row flex-wrap gap-y-5 gap-x-11 justify-around mt-4'>
                        <View className="items-center">
                            <Image
                                source={require("../../assets/images/ProfilePic.png")}
                                className="h-[80px] w-[80px] rounded-full mb-1"
                                resizeMode="cover"
                            />
                            <Text className="text-xs text-[#787A80] text-center">
                                {`${(activeTrip?.data?.driver || trip?.driver)?.firstName}`}
                            </Text>
                            <Text className="text-xs text-[#787A80] text-center">
                                {`${(activeTrip?.data?.driver || trip?.driver)?.lastName}`}
                            </Text>
                            <Text className="text-xs text-[#787A80] text-center">
                                (Driver)
                            </Text>
                        </View>
                        {Array.from({ length: ((activeTrip?.data?.driver || trip?.driver)?.carDetails?.passengerCapacity ?? 0) }).map((_, index) => {
                            const passenger = trip?.passengers?.[index];
                            return (
                                <View key={index} className="items-center">
                                    <Image
                                        source={passenger ? require("../../assets/images/ProfilePic.png") : require("../../assets/images/emptyNew.png")}
                                        className="h-[80px] w-[80px] rounded-full mb-1"
                                        resizeMode="cover"
                                    />
                                    <Text className="text-xs text-[#787A80] text-center">
                                        {passenger ? `${passenger.firstName} ${passenger.lastName}` : "Empty seat"}
                                    </Text>
                                </View>
                            );
                        })}
                    </View>
                </View>
            ) : (
            <KeyboardAvoidingView 
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                className='flex-1'
                keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
            >
                <ScrollView 
                    className='flex-1' 
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingBottom: 120 }}
                >
                        <TripRouteCard 
                            trip={trip} 
                            arrivalTime={arrivalTime} 
                            tripStarted={tripStarted}
                            tripProgress={tripProgress}
                        />
                        <TripDetailsCard trip={trip} />

                        <TouchableOpacity className='flex-row items-center justify-between mt-2 bg-white rounded-md p-4 mx-4' onPress={() => {setShowSeats(true); console.log(trip)}}>
                            <Text className='text-[#999999] font-medium text-sm'>Booked by:</Text>
                            <View className='flex-row items-center'>
                                {(trip?.passengers?.length ?? 0) > 0 ? (
                                <Image
                                    source={require("../../assets/images/ProfilePic.png")}
                                    className="h-[28px] w-[28px] rounded-full mr-3"
                                    resizeMode="cover"
                                />
                                ) : (
                                <Image
                                    source={require("../../assets/images/emptyNew.png")}
                                    className="h-[28px] w-[28px] rounded-full mr-3"
                                    resizeMode="cover"
                                />
                                )}
                                <Image source={require("../../assets/images/goNew.png")} className="h-[20px] w-[20px]" />
                            </View>
                        </TouchableOpacity>
                        {!(activeTrip?.data?.driver && trip?.driver) && (
                        <TemporaryDriverDetails />
                        )}

                        {(activeTrip?.data?.driver || trip?.driver) && (
                        <View className='px-4 mt-2'>
                            <View className='bg-white p-4 rounded-[6px]'>
                                <View className='flex flex-row gap-3 items-center'>
                                    <View className='h-10 w-10 bg-black rounded-full' />
                                    <View className='flex-1 flex-col'>
                                        <Text className='font-semibold text-base'>
                                                {(activeTrip?.data?.driver || trip?.driver)?.firstName} {(activeTrip?.data?.driver || trip?.driver)?.lastName}
                                        </Text>
                                        <View className='flex flex-row items-center'>
                                            <Text className='font-light text-sm text-[#787A80]'>
                                                    {`${(activeTrip?.data?.driver || trip?.driver)?.carDetails.make}${(activeTrip?.data?.driver || trip?.driver)?.carDetails.model}, ${(activeTrip?.data?.driver || trip?.driver)?.carDetails.colour}`}
                                            </Text>
                                            <Text className='font-medium ml-2 text-xs p-1 rounded-md text-black bg-[#EAEAEA]'>
                                                    {(activeTrip?.data?.driver || trip?.driver)?.carDetails?.plateNumber}
                                            </Text>
                                            </View>
                                    </View>
                                </View>
                                
                                <View className='mt-4'>
                                    <Text className='text-sm font-medium text-[#787A80] mb-2'>Driver's Note</Text>
                                    <Text className='bg-[#F4F4F4] p-3 rounded-md text-sm'>
                                        {(activeTrip?.data?.driverNote || trip?.driverNote) || 'No note added'}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    )}

                    {rideRequests?.data && rideRequests.data.length > 0 && (
                    <View className="px-4 mt-2">
                        
                        <View className="bg-white p-4 rounded-[6px]">
                        <Text className="font-semibold text-base mb-3">Booking requests</Text>
                        {rideRequests.data.map((request: any) => (
                            <View
                            key={request.id}
                            className="flex flex-col justify-center mb-3"
                            >
                            <View className="flex-row items-center mb-2">
                                <TouchableOpacity onPress={() => console.log(rideRequests.data)}>
                                    <View className="h-8 w-8 bg-[#EDEDED] rounded-full mr-2 justify-center items-center">
                                    <Text className="text-lg font-bold">
                                        {request.firstName}
                                    </Text>
                                    </View>
                                </TouchableOpacity>
                                
                                <Text className="font-medium text-sm">
                                {request.firstName} wants to join your trip
                                </Text>
                            </View>
                            <View className="flex-row items-center space-x-[10px]">
                                {requestStatuses[request.requestId]?.status === "accepted" ? (
                                    requestStatuses[request.requestId]?.pickupConfirmed ? (
                                        <View className='rounded-full p-2'>
                                            <Text className='text-green-500 text-[10px]'>Pickup Confirmed</Text>
                                        </View>
                                    ) : (
                                        <Button
                                            text="Confirm Pickup"
                                            buttonClassName="bg-[#473BF0]"
                                            textClassName="text-white"
                                            width="w-[150px]"
                                            height="h-[34px]"
                                            onClick={() => confirmPickUp({ dataBody: { requestId: request.requestId } })}
                                            isLoading={isConfirmingPickup}
                                        />
                                    )
                                ) : requestStatuses[request.requestId]?.status === "rejected" ? (
                                    <View className='rounded-full p-2'>
                                        <Text className='text-red-500 text-[10px]'>Request Declined</Text>
                                    </View>
                                ) : (
                                    <View className='flex-row'>
                                        <Button
                                            text="Decline"
                                            buttonClassName="bg-[#EDEDED] mr-[10px]"
                                            textClassName="text-[#151B2D]"
                                            width="w-[115px]"
                                            height="h-[34px]"
                                            onClick={() => handleRejectRideRequest(request.requestId)}
                                            isLoading={loadingRequestId.id === request.requestId && loadingRequestId.type === "reject"}
                                        />
                                        <Button
                                            text="Accept"
                                            buttonClassName="bg-[#34A853]"
                                            textClassName="text-white"
                                            width="w-[115px]"
                                            height="h-[34px]"
                                            onClick={() => handleAcceptRideRequest(request.requestId)}
                                            isLoading={loadingRequestId.id === request.requestId && loadingRequestId.type === "accept"}
                                        />
                                    </View>
                                )}
                                

                            <TouchableOpacity
                            activeOpacity={0.8}
                            onPress={() => router.push(`/${"chat"}`)}
                            className="items-center justify-center"
                            >   
                            <Image
                                source={require("../../assets/images/Chat.png")}
                                className="w-[34px] h-[34px]"
                                resizeMode="contain"
                            />
                            </TouchableOpacity>

                            <TouchableOpacity
                            onPress={() => makePhoneCall(request.phoneNumber)}
                            activeOpacity={0.8}
                            className="items-center justify-center"
                            >
                            <Image
                                source={require("../../assets/images/Call.png")}
                                className="w-[34px] h-[34px]"
                                resizeMode="contain"
                            />
                            </TouchableOpacity>
                            </View>
                            </View>
                        ))}
                        </View>
                    </View>
                    )}

                    {trip.preferences && (
                        <View className='px-4 mt-2'>
                            <View className='bg-white p-4 rounded-[6px]'>
                                <Text className='font-semibold text-base mb-3'>Trip preferences</Text>
                                <View className='gap-y-3'>
                                    {trip.preferences.map((preference, index) => (
                                        <View key={index} className='flex flex-row justify-between items-center'>
                                                <View className='flex-row items-center'>
                                                    {preference.desc === "Allow luggage" ? (
                                                    <Image
                                                        source={require("../../assets/images/luggageNew.png")}
                                                        className="h-[26px] w-[26px]"
                                                        resizeMode="contain"
                                                    />
                                                    ) : (
                                                    preference.desc === "Allow Smoking/Drinking" ? (
                                                        <Image
                                                        source={require("../../assets/images/smokingNew.png")}
                                                        className="h-[26px] w-[26px]"
                                                        resizeMode="contain"
                                                        />
                                                    ) : (
                                                        preference.desc === "Allow Pets" ? (
                                                        <Image
                                                            source={require("../../assets/images/petsNew.png")}
                                                            className="h-[26px] w-[26px]"
                                                            resizeMode="contain"
                                                        />
                                                        ) : (
                                                        <Image
                                                            source={require("../../assets/images/bikesNew.png")}
                                                            className="h-[26px] w-[26px]"
                                                            resizeMode="contain"
                                                        />
                                                        )
                                                    )
                                                    )}
                                                    <Text className="text-[14px] font-medium ml-2">
                                                    {capitalizeFirstLetter(preference.desc.split(" ")[1])}
                                            </Text>
                                                </View>
                                                
                                                {preference.value === true ? (
                                                    <View className='flex-row items-center'>
                                                        <Image
                                                        source={require("../../assets/images/accepted.png")}
                                                        className="h-[12px] w-[12px] mr-1"
                                                        resizeMode="contain"
                                                        />
                                                        <Text className='text-[#34A853] font-medium text-sm'>Yes</Text>
                                                    </View>
                                                    
                                                ) : (
                                                    <View className='flex-row items-center'>
                                                        <Image
                                                        source={require("../../assets/images/cancel.png")}
                                                        className="h-[12px] w-[12px] mr-1"
                                                        resizeMode="contain"
                                                        />
                                                        <Text className='text-[#787A80] font-medium text-sm'>No</Text>
                                                    </View>
                                                )}
                                        </View>
                                    ))}
                                    </View>
                            </View>
                        </View>
                    )}

                    {tripStarted && isDriver && <PassengersPaymentSection />}

                    <View className='mt-2 px-4'>
                        <MapView
                            region={initialRegion}
                            userInterfaceStyle="light"
                            showsUserLocation={true}
                            className="w-full h-[150px] rounded-lg"
                            initialRegion={initialRegion}
                            provider={PROVIDER_DEFAULT}
                        />
                    </View>
                </ScrollView>

                
                <View className="px-4 pb-6 bg-white">
                    {renderBottomButton()}
                </View>

                {renderModals()}
            </KeyboardAvoidingView>
            )}
        </View>
    );
};

export default TripSummary;